using System;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer;
using RxjhServer.Database;
using RxjhServer.DbClss;
using RxjhServer.HelperTools;

public class CongThanhChien : IDisposable
{
	private System.Timers.Timer timer_0;

	private System.Timers.Timer timer_1;

	private System.Timers.Timer timer_2;

	private System.Timers.Timer timer_3;

	private DateTime dateTime_0;

	private DateTime dateTime_1;

	private DateTime dateTime_2;

	private DateTime dateTime_4;

	public bool HoaLong_ChiLuc_GiaiPhong;

	public static DateTime KhiTienLenTrinh_KetThuc_ThoiGian;

	public string CTC_Old_NguoiChiemGiu_Den_Tenma;

	public NpcClass ThuThanh_PhoTuong;

	public CongThanhChien()
	{
		ThuThanh_PhoTuong = null;
		try
		{
			if (World.jlMsg == 1)
			{
				LogHelper.WriteLine(0, "Mới CongThanhChien Bắt đầu");
			}
			World.ThienMaThanCungDaiMon_PhaiChangTuVong = 0;
			World.ThienMaThanCungDongMon_PhaiChangTuVong = 0;
			dateTime_4 = DateTime.Now;
			dateTime_0 = DateTime.Now.AddMinutes(World.CongThanhChien_ThoiGianChuanBi);
			KhiTienLenTrinh_KetThuc_ThoiGian = dateTime_0;
			World.CongThanhChien_Progress = 1;
			timer_0 = new(10000.0);
			timer_0.Elapsed += ThoiGianKetThucSuKien1;
			timer_0.Enabled = true;
			timer_0.AutoReset = true;
			var npcClass = World.AddNpc_CongThanhChien(16430, -430f, -393f, 42001, QuaiXuatHien_DuyNhatMotLan: true, 600);
			if (npcClass != null)
			{
				npcClass.Max_Rxjh_HP += World.Cong_Thanh_CuongHoa_Level * 250000;
				npcClass.Rxjh_HP = npcClass.Max_Rxjh_HP;
				npcClass.FLD_DF += World.Cong_Thanh_CuongHoa_Level * 50;
			}
			var npcClass2 = World.AddNpc_CongThanhChien(16431, 50f, 468f, 42001, QuaiXuatHien_DuyNhatMotLan: true, 600);
			if (npcClass2 != null)
			{
				npcClass2.Max_Rxjh_HP += World.Cong_Thanh_CuongHoa_Level * 250000;
				npcClass2.Rxjh_HP = npcClass2.Max_Rxjh_HP;
				npcClass2.FLD_DF += World.Cong_Thanh_CuongHoa_Level * 50;
			}
			ThuThanh_PhoTuong = World.AddNpc_CongThanhChien(16435, -436f, 530f, 42001, QuaiXuatHien_DuyNhatMotLan: true, 600);
			CTC_Old_NguoiChiemGiu_Den_Tenma = World.NguoiChiemGiu_Den_Tenma;
			if (World.TLC_CTC_BatDau_QuangBa == 1)
			{
				World.conn.Transmit("活动开启|CongThanhChien|" + World.ServerGroupID + "|" + World.ServerID + "|0|0");
			}
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.ThongBao_CongThanh == 1 && value.GuildName != "" && value.Player_Job_level >= 2 && !value.Client.TreoMay)
				{
					value.GuiDi_TheLucChien_DemNguoc(World.CongThanhChien_ThoiGianChuanBi);
					value.GuiDi_LoiMoi_CongThanhChien_Lan_1(value);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Công Thành Chiến Bắt đầu Phạm sai lầm：" + ex.StackTrace);
		}
	}

	public void ThoiGianKetThucSuKien1(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "Công Thành Chiến thời gian kết thúc [1]");
		}
		try
		{
			var num = (int)dateTime_0.Subtract(DateTime.Now).TotalSeconds;
			if (num > 0)
			{
				return;
			}
			World.CongThanhChien_Progress = 2;
			num = 0;
			timer_0.Enabled = false;
			timer_0.Close();
			timer_0.Dispose();
			dateTime_1 = DateTime.Now.AddMinutes(World.CongThanhChien_ThoiGianChuanBi);
			KhiTienLenTrinh_KetThuc_ThoiGian = dateTime_1;
			timer_1 = new(10000.0);
			timer_1.Elapsed += ThoiGianKetThucSuKien2;
			timer_1.Enabled = true;
			timer_1.AutoReset = true;
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.NhanVatToaDo_BanDo == 42001 && !value.Client.TreoMay)
				{
					value.GuiDi_CongThanhChien_ThoiGian_ConLai(180);
				}
				else if (value.ThongBao_CongThanh == 1 && value.GuildName != "" && value.Player_Job_level >= 2 && !value.Client.TreoMay)
				{
					value.GuiDi_LoiMoi_CongThanhChien_Lan_2(value);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Công Thành Chiến kết thúc thời gian sự kiện bị lỗi： " + ex.StackTrace);
		}
	}

	public void ThoiGianKetThucSuKien2(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "Công Thành Chiến kết thúc thời gian sự kiện [2]");
		}
		try
		{
			var num = (int)dateTime_1.Subtract(DateTime.Now).TotalSeconds;
			if (World.CongThanhChien_Progress == 3)
			{
				num = 0;
			}
			if (num > 0)
			{
				return;
			}
			World.CongThanhChien_Progress = 3;
			num = 0;
			timer_1.Enabled = false;
			timer_1.Close();
			timer_1.Dispose();
			dateTime_2 = DateTime.Now.AddMinutes(World.CongThanhChien_TongThoiGian);
			KhiTienLenTrinh_KetThuc_ThoiGian = dateTime_2;
			timer_2 = new(10000.0);
			timer_2.Elapsed += ThoiGianKetThucSuKien3;
			timer_2.Enabled = true;
			timer_2.AutoReset = true;
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.NhanVatToaDo_BanDo == 42001 && !value.Client.TreoMay)
				{
					value.SwitchPkMode(1);
					value.GuiDi_CongThanhChien_ThoiGian_ConLai(900);
					if (value.MonPhai_LienMinh_MinhChu == World.NguoiChiemGiu_Den_Tenma)
					{
						value.Mobile(-437f, 89f, 15f, 42001, 0);
					}
					else
					{
						value.Mobile(-430f, -660f, 15f, 42001, 0);
					}
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Công Thành Chiến thời gian kết thúc event 2 lỗi： - " + ex.StackTrace);
		}
	}

	public void ThoiGianKetThucSuKien3(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "Công Thành Chiến thời gian kết thúc event 3");
		}
		try
		{
			var num = (int)dateTime_2.Subtract(DateTime.Now).TotalSeconds;
			if (World.CongThanhChien_Progress == 2)
			{
				num = 0;
			}
			if (DateTime.Now.Subtract(dateTime_4).TotalSeconds > 3600.0)
			{
				num = 0;
			}
			if (num > 0)
			{
				return;
			}
			num = 0;
			timer_2.Enabled = false;
			timer_2.Close();
			timer_2.Dispose();
			if (World.CongThanhChien_Progress == 3)
			{
				if (World.NguoiChiemGiu_Den_Tenma != "")
				{
					if (World.NguoiChiemGiu_Den_Tenma == CTC_Old_NguoiChiemGiu_Den_Tenma)
					{
						RxjhClass.CapNhat_ThienMaThanCung_TinTuc(World.NguoiChiemGiu_Den_Tenma, World.BanDau_ChiemLinh_Ngay.ToString(), 0);
						foreach (var value in World.allConnectedChars.Values)
						{
							if (value.NhanVatToaDo_BanDo == 42001 && !value.Client.TreoMay)
							{
								value.GuiDi_Thu_Thanh_ThanhCong(9, World.NguoiChiemGiu_Den_Tenma);
							}
						}
					}
					else
					{
						World.BanDau_ChiemLinh_Ngay = int.Parse(Converter.DateTimeToString(DateTime.Now));
						RxjhClass.CapNhat_ThienMaThanCung_TinTuc(World.NguoiChiemGiu_Den_Tenma, Converter.DateTimeToString(DateTime.Now), 0);
						foreach (var value2 in World.allConnectedChars.Values)
						{
							if (value2.NhanVatToaDo_BanDo == 42001 && !value2.Client.TreoMay)
							{
								value2.GuiDi_Thu_Thanh_ThanhCong(11, World.NguoiChiemGiu_Den_Tenma);
								if (value2.MonPhai_LienMinh_MinhChu == CTC_Old_NguoiChiemGiu_Den_Tenma)
								{
									value2.GuiDi_CongThanhChien_TuongQuan_BUFF(value2, isDisappear: false);
								}
								else if (value2.MonPhai_LienMinh_MinhChu == World.NguoiChiemGiu_Den_Tenma)
								{
									value2.GuiDi_CongThanhChien_TuongQuan_BUFF(value2, isDisappear: false);
								}
							}
						}
					}
				}
				foreach (var value3 in World.allConnectedChars.Values)
				{
					if (value3.NhanVatToaDo_BanDo != 42001 || value3.Client.TreoMay)
					{
						continue;
					}
					if (value3.MonPhai_LienMinh_MinhChu == World.NguoiChiemGiu_Den_Tenma)
					{
						var num2 = 11000;
						value3.Player_WuXun += num2;
						value3.HeThongNhacNho("Đại hiệp được Thiên Cơ Các ban thưởng [" + num2 + "] võ huân, công lao vang danh tứ hải!", 20, "Thiên cơ các");
						RxjhClass.TangThemVoHuan_Record(value3.AccountID, value3.CharacterName, 20000, "天魔神宫Thang利");
						var parcelVacancy = value3.GetParcelVacancy(value3);
						if (parcelVacancy != -1)
						{
							value3.AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(World.PhanThuongBenThangTLC), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 30);
						}
						else
						{
							value3.HeThongNhacNho("Hành trang của đại hiệp đã đầy, không còn chỗ chứa thêm bảo vật!", 20, "Thiên cơ các");
						}
					}
					else
					{
						var num3 = 8000;
						value3.Player_WuXun += num3;
						value3.HeThongNhacNho("Đại hiệp được Thiên Cơ Các ban thưởng [" + num3 + "] võ huân, danh chấn giang hồ!", 20, "Thiên cơ các");
						RxjhClass.TangThemVoHuan_Record(value3.AccountID, value3.CharacterName, 15000, "天魔神宫Thang利");
						var parcelVacancy2 = value3.GetParcelVacancy(value3);
						if (parcelVacancy2 != -1)
						{
							value3.AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(World.PhanThuongBenThuaTLC), parcelVacancy2, BitConverter.GetBytes(1), new byte[56], khoaLai: false, 30);
						}
						else
						{
							value3.HeThongNhacNho("Hành trang của đại hiệp đã đầy, không còn chỗ chứa thêm bảo vật!", 20, "Thiên cơ các");
						}
					}
					value3.GuiDi_CongThanhChien_ThoiGian_ConLai(60);
					value3.HeThongNhacNho("Sau một phút nữa, kẻ du hành sẽ bị Thiên Cơ Các trục xuất khỏi chốn võ lâm!", 10, "Thiên cơ các");
				}
				World.CongThanhChien_Progress = 4;
				timer_3 = new(60000.0);
				timer_3.Elapsed += ThoiGianKetThucSuKien4;
				timer_3.Enabled = true;
				timer_3.AutoReset = true;
			}
			else
			{
				if (World.CongThanhChien_Progress != 2)
				{
					return;
				}
				World.AddNpc_CongThanhChien(16430, -430f, -393f, 42001, QuaiXuatHien_DuyNhatMotLan: true, 600);
				World.AddNpc_CongThanhChien(16431, 50f, 468f, 42001, QuaiXuatHien_DuyNhatMotLan: true, 600);
				ThuThanh_PhoTuong = World.AddNpc_CongThanhChien(16435, -436f, 530f, 42001, QuaiXuatHien_DuyNhatMotLan: true, 600);
				World.CongThanhChien_Progress = 3;
				dateTime_2 = DateTime.Now.AddMinutes(5.0);
				KhiTienLenTrinh_KetThuc_ThoiGian = dateTime_2;
				timer_2 = new(10000.0);
				timer_2.Elapsed += ThoiGianKetThucSuKien3;
				timer_2.Enabled = true;
				timer_2.AutoReset = true;
				{
					foreach (var value4 in World.allConnectedChars.Values)
					{
						if (value4.NhanVatToaDo_BanDo != 42001 && !value4.Client.TreoMay)
						{
							continue;
						}
						if (value4.CharacterPKMode != 2)
						{
							var string_ = "AA550800720516100200010055AA";
							var array = Converter.HexStringToByte(string_);
							value4.CharacterPKMode = 2;
							if (value4.Client != null)
							{
								value4.Client.Send_Map_Data(array, array.Length);
							}
						}
						value4.GuiDi_CongThanhChien_ThoiGian_ConLai(300);
						if (value4.MonPhai_LienMinh_MinhChu == World.NguoiChiemGiu_Den_Tenma)
						{
							value4.Mobile(-437f, 89f, 15f, 42001, 0);
						}
						else
						{
							value4.Mobile(-430f, -660f, 15f, 42001, 0);
						}
					}
					return;
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Cong Thanh Chien Event 3 Lỗi：" + ex.StackTrace);
		}
	}

	public void ThoiGianKetThucSuKien4(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "Cong Thanh Chien Event 4");
		}
		try
		{
			timer_3.Enabled = false;
			timer_3.Close();
			timer_3.Dispose();
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.NhanVatToaDo_BanDo == 42001 && !value.Client.TreoMay)
				{
					if (World.WhetherTheCurrentLineIsSilver == 1 && World.CoMo_ThiTruongTraoDoiTienXu == 1)
					{
						value.Mobile(10f, 10f, 15f, 1201, 0);
					}
					else
					{
						value.Mobile(420f, 1550f, 15f, 101, 0);
					}
				}
			}
			Dispose();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "CongThanhChien Event 4 Lỗi：" + ex.StackTrace);
		}
	}

	public void Dispose()
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "Cong Thanh Chien Dispose");
		}
		World.CongThanhChien_Progress = 0;
		if (timer_0 != null)
		{
			timer_0.Enabled = false;
			timer_0.Close();
			timer_0.Dispose();
		}
		if (timer_1 != null)
		{
			timer_1.Enabled = false;
			timer_1.Close();
			timer_1.Dispose();
		}
		if (timer_2 != null)
		{
			timer_2.Enabled = false;
			timer_2.Close();
			timer_2.Dispose();
		}
		if (timer_3 != null)
		{
			timer_3.Enabled = false;
			timer_3.Close();
			timer_3.Dispose();
		}
		foreach (var value in World.allConnectedChars.Values)
		{
			if (value.NhanVatToaDo_BanDo == 42001 && !value.Client.TreoMay)
			{
				if (World.WhetherTheCurrentLineIsSilver == 1 && World.CoMo_ThiTruongTraoDoiTienXu == 1)
				{
					value.Mobile(10f, 10f, 15f, 1201, 0);
				}
				else
				{
					value.Mobile(420f, 1550f, 15f, 101, 0);
				}
			}
		}
		World.CongThanhChien_BatDau = null;
		World.delNpc(42001, 16430);
		World.delNpc(42001, 16431);
		World.delNpc(42001, 16435);
	}
}
