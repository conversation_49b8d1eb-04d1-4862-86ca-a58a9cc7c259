<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:controls="using:HeroYulgang.Controls"
        mc:Ignorable="d" d:DesignWidth="1000" d:DesignHeight="600"
        x:Class="HeroYulgang.MainWindow"
        x:CompileBindings="False"
        Title="YulgangHero" MinWidth="800" MinHeight="500">

    <Grid RowDefinitions="Auto,*">
        <!-- Header with server status and controls -->
        <Grid Grid.Row="0" ColumnDefinitions="Auto,*,Auto" Margin="10">
            <controls:ServerStatusIndicator Grid.Column="0" Width="300" Margin="0,0,10,0"/>

            <StackPanel Grid.Column="2" Orientation="Horizontal" Spacing="10">
                <Button x:Name="StartServerButton" Content="Khởi Động" Width="120" Height="35"/>
                <Button x:Name="StopServerButton" Content="Dừng" Width="120" Height="35"/>
                <Button x:Name="RestartServerButton" Content="Khởi Động Lại" Width="120" Height="35"/>
                <Button x:Name="AutoOfflineButton" Content="Auto Offline" Width="120" Height="35"/>
                <Button x:Name="AutoPartyButton" Content="Auto Party" Width="120" Height="35"/>
            </StackPanel>
        </Grid>

        <!-- Log viewer -->
        <controls:LogViewer Grid.Row="1" Margin="10"/>
    </Grid>
</Window>
