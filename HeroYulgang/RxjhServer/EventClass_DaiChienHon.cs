using System;
using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer.Database;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class EventClass_DaiChienHon : IDisposable
{
	private string jlsqlzj = string.Empty;

	private System.Timers.Timer ThoiGian1;

	private System.Timers.Timer ThoiGian2;

	private System.Timers.Timer ThoiGian3;

	private System.Timers.Timer ThoiGian4;

	private System.Timers.Timer ThoiGian5;

	private System.Timers.Timer ThoiGian6;

	private DateTime kssj;

	private DateTime kssjgj;

	private DateTime kssjgj2;

	private DateTime timetranchien;

	private DateTime timechoketthuc;

	private int kssjint;

	public EventClass_DaiChienHon()
	{
		try
		{
			DBA.ExeSqlCommand("DELETE FROM EventTop_DCH");
			World.DanhSachNhanVat_ThamGia_DCH.Clear();
			World.DCH_ToaDo_UnCheck.Clear();
			World.DCH_ToaDo_TanHinh.Clear();
			World.NpcEvent_DCH.Clear();
			World.EventTopDCH.Clear();
			World.DCH_ChinhPhai_DiemSo = 0;
			World.DCH_TaPhai_DiemSo = 0;
			SetUp_ToaDoTanHinh_1();
			Install_Event_Cong_va_Tru();
			World.DCH_Progress = 1;
			kssj = DateTime.Now.AddSeconds(World.ThoiGian_ChuanBi_DCH);
			World.DCH_TaPhai_DiemSo = 1800000;
			World.DCH_ChinhPhai_DiemSo = 1800000;
			ThoiGian1 = new(1000.0);
			ThoiGian1.Elapsed += ThoiGianKetThucSuKien1;
			ThoiGian1.Enabled = true;
			ThoiGian1.AutoReset = true;
			ThoiGianKetThucSuKien1(null, null);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Đại Chiến Hồn 00 error：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien1(object sender, ElapsedEventArgs e)
	{
		try
		{
			var int_ = (kssjint = (int)kssj.Subtract(DateTime.Now).TotalSeconds);
			foreach (var value2 in World.allConnectedChars.Values)
			{
				if (!World.DanhSachNhanVat_ThamGia_DCH.TryGetValue(value2.SessionID, out var _) && !value2.Client.TreoMay && value2.Player_Job_level >= 2 && value2.NhanVatToaDo_BanDo != 40101)
				{
					World.DanhSachNhanVat_ThamGia_DCH.Add(value2.SessionID, value2);
				}
				else
				{
					Time_Dem_Nguoc(value2, int_);
				}
			}
			if (kssjint <= 0)
			{
				ThoiGian1.Enabled = false;
				ThoiGian1.Close();
				ThoiGian1.Dispose();
				World.DCH_Progress = 2;
				kssjgj = DateTime.Now.AddSeconds(2.0);
				ThoiGian2 = new(3000.0);
				ThoiGian2.Elapsed += ThoiGianKetThucSuKien2;
				ThoiGian2.Enabled = true;
				ThoiGian2.AutoReset = true;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Đại Chiến Hồn 11 error：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien2(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (int)kssjgj.Subtract(DateTime.Now).TotalSeconds;
			foreach (var value in World.DanhSachNhanVat_ThamGia_DCH.Values)
			{
				if (value.NhanVatToaDo_BanDo != 40101)
				{
					value.GuiDi_DCH_LoiMoi_New2();
				}
			}
			if (num <= 0)
			{
				ThoiGian2.Enabled = false;
				ThoiGian2.Close();
				ThoiGian2.Dispose();
				World.DCH_Progress = 3;
				kssjgj2 = DateTime.Now.AddSeconds(World.ThoiGian_ChuanBi_DCH);
				ThoiGian3 = new(1000.0);
				ThoiGian3.Elapsed += ThoiGianKetThucSuKien3;
				ThoiGian3.Enabled = true;
				ThoiGian3.AutoReset = true;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Đại Chiến Hồn 22 error：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien3(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (int)kssjgj2.Subtract(DateTime.Now).TotalSeconds;
			foreach (var value in World.DanhSachNhanVat_ThamGia_DCH.Values)
			{
				if (value.NhanVatToaDo_BanDo == 40101)
				{
					value.HeThongNhacNho("Đại Chiến Hồn khai mở sau: " + num + " giây, đại hiệp hãy chuẩn bị tinh thần chinh chiến!", 10, "Thiên cơ các");
				}
			}
			if (num > 0)
			{
				return;
			}
			timetranchien = DateTime.Now.AddMinutes(World.TongThoiGian_DCH);
			Remove_Event_NPC_Cua();
			Install_Event_Monster_va_Boss();
			foreach (var value2 in World.DanhSachNhanVat_ThamGia_DCH.Values)
			{
				if (value2.NhanVatToaDo_BanDo == 40101)
				{
					DCH_BATDAU(value2);
					DCH_BATDAU_TIME(value2, World.TongThoiGian_DCH);
					DCH_UPDATE_DIEMSO(value2, World.DCH_ChinhPhai_DiemSo, World.DCH_TaPhai_DiemSo);
					if (value2.CharacterPKMode != 1)
					{
						value2.SwitchPkMode(1);
					}
				}
			}
			ThoiGian3.Enabled = false;
			ThoiGian3.Close();
			ThoiGian3.Dispose();
			World.DCH_Progress = 4;
			ThoiGian4 = new(5000.0);
			ThoiGian4.Elapsed += ThoiGianKetThucSuKien4;
			ThoiGian4.Enabled = true;
			ThoiGian4.AutoReset = true;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Đại Chiến Hồn 33 error：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien4(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (World.DCH_MidTime = (int)timetranchien.Subtract(DateTime.Now).TotalSeconds);
			foreach (var value in World.DanhSachNhanVat_ThamGia_DCH.Values)
			{
				if (value.NhanVatToaDo_BanDo == 40101)
				{
					DCH_UPDATE_DIEMSO(value, World.DCH_ChinhPhai_DiemSo, World.DCH_TaPhai_DiemSo);
				}
				else
				{
					value.Disconnect_VaoLai_DaiChienHon();
				}
			}
			if (World.DCH_ChinhPhai_DiemSo <= 0 || World.DCH_TaPhai_DiemSo <= 0)
			{
				num = 0;
			}
			if (num > 0)
			{
				return;
			}
			foreach (var value2 in World.DanhSachNhanVat_ThamGia_DCH.Values)
			{
				if (value2.NhanVatToaDo_BanDo == 40101)
				{
					DCH_KET_THUC(value2, World.DCH_ChinhPhai_DiemSo, World.DCH_TaPhai_DiemSo);
					value2.DCH_HP_BONUS = 0;
					value2.DCH_ATTACK_BONUS = 0;
					value2.DCH_DEF_BONUS = 0;
					value2.DCH_CLVC_BONUS = 0.0;
					value2.DCH_ULPT_BONUS = 0.0;
					value2.DCH_StackC = 0;
					value2.DCH_StackB = 0;
					value2.DCH_StackA = 0;
					value2.DCH_StackA_SoLuong = 0;
					value2.DCH_StackB_SoLuong = 0;
					value2.DCH_StackC_SoLuong = 0;
					value2.DCH_Stack_Add(value2, **********, -1, 0);
					value2.DCH_Stack_Add(value2, **********, -1, 0);
					value2.DCH_Stack_Add(value2, **********, -1, 0);
					value2.CapNhat_HP_MP_SP();
				}
			}
			World.DCH_Progress = 5;
			ThoiGian4.Enabled = false;
			ThoiGian4.Close();
			ThoiGian4.Dispose();
			timechoketthuc = DateTime.Now.AddSeconds(1.0);
			ThoiGian5 = new(1000.0);
			ThoiGian5.Elapsed += ThoiGianKetThucSuKien5;
			ThoiGian5.Enabled = true;
			ThoiGian5.AutoReset = true;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Đại Chiến Hồn 44 error：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien5(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (int)timechoketthuc.Subtract(DateTime.Now).TotalSeconds;
			foreach (var value in World.DanhSachNhanVat_ThamGia_DCH.Values)
			{
				value.DCH_HP_BONUS = 0;
				value.DCH_ATTACK_BONUS = 0;
				value.DCH_DEF_BONUS = 0;
				value.DCH_CLVC_BONUS = 0.0;
				value.DCH_ULPT_BONUS = 0.0;
			}
			if (num <= 0)
			{
				ThoiGian5.Enabled = false;
				ThoiGian5.Close();
				ThoiGian5.Dispose();
				World.DCH_Progress = 6;
				ThoiGian6 = new(5000.0);
				ThoiGian6.Elapsed += ThoiGianKetThucSuKien6;
				ThoiGian6.Enabled = true;
				ThoiGian6.AutoReset = false;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Đại Chiến Hồn 55 error：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien6(object sender, ElapsedEventArgs e)
	{
		try
		{
			foreach (var value2 in World.EventTopDCH.Values)
			{
				var tenNhanVat = value2.TenNhanVat;
				var dBToDataTable = DBA.GetDBToDataTable($"SELECT TenNhanVat from [EventTop_DCH] where TenNhanVat ='{tenNhanVat}'");
				DBA.ExeSqlCommand($"INSERT INTO EventTop_DCH (TenNhanVat,BangPhai,TheLuc,DangCap,GietNguoiSoLuong,TuVongSoLuong, Dame_Tru, HopTacGietNguoi,Diem_DCH_ChinhPhai,Diem_DCH_TaPhai)values('{tenNhanVat}','{value2.BangPhai}','{value2.TheLuc}',{value2.DangCap},{value2.GietNguoiSoLuong},{value2.TuVongSoLuong},{value2.Dame_Tru},{value2.HopTacGietNguoi},{World.DCH_ChinhPhai_DiemSo},{World.DCH_TaPhai_DiemSo})");
			}
			if (World.DCH_ChinhPhai_DiemSo > World.DCH_TaPhai_DiemSo)
			{
				jlsqlzj = "CHINHPHAI";
				World.GuiThongBao("ĐẠI CHIẾN HỒN kết thúc. Chính Phái dành chiến thắng");
			}
			else if (World.DCH_ChinhPhai_DiemSo == World.DCH_TaPhai_DiemSo)
			{
				jlsqlzj = string.Empty;
				World.GuiThongBao("ĐẠI CHIẾN HỒN kết thúc 2 bên hòa nhau");
			}
			else
			{
				jlsqlzj = "TAPHAI";
				World.GuiThongBao("ĐẠI CHIẾN HỒN kết thúc, Tà Phái dành chiến thắng");
			}
			foreach (var value3 in World.allConnectedChars.Values)
			{
				if (value3.NhanVatToaDo_BanDo != 40101)
				{
					continue;
				}
				if (DateTime.Now.DayOfWeek.ToString() == "Monday" || DateTime.Now.DayOfWeek.ToString() == "Wednesday" || DateTime.Now.DayOfWeek.ToString() == "Friday")
				{
					value3.TongHop_Top_Solo_DCH();
				}
				var num = 0.0;
				if (World.EventTopDCH.TryGetValue(value3.CharacterName, out var value))
				{
					num = value.Dame_Tru * 0.1 / 2.0;
					if (num > 3000.0)
					{
						num = 3000.0;
					}
				}
				if (value3.DCH_PhePhai == jlsqlzj)
				{
					value3.Player_WuXun += World.Win_DCH_Phan_Thuong_VoHuan + (int)num;
					value3.HeThongNhacNho("Đại hiệp nhận được [" + World.Win_DCH_Phan_Thuong_VoHuan + "] điểm võ huân, chiến tích vang danh!", 10, "Truyền Âm Các");
					value3.HeThongNhacNho("Đại hiệp nhận thêm [" + (int)num + "] võ huân từ Trụ Đại Chiến Hồn, quả là bậc anh hùng!", 23, "Truyền Âm Các");
					var parcelVacancy = value3.GetParcelVacancy(value3);
					if (parcelVacancy != -1)
					{
						value3.AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008001836), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 5);
					}
					else
					{
						value3.HeThongNhacNho("Hành trang không còn chỗ trống!", 10, "Thiên cơ các");
					}
				}
				else
				{
					value3.Player_WuXun += World.Lose_DCH_Phan_Thuong_VoHuan + (int)num;
					value3.HeThongNhacNho("Đại hiệp nhận được [" + World.Lose_DCH_Phan_Thuong_VoHuan + "] điểm võ huân, dù bại vẫn hiển vinh!", 10, "Truyền Âm Các");
					value3.HeThongNhacNho("Đại hiệp nhận thêm [" + (int)num + "] võ huân từ Trụ Đại Chiến Hồn, quả là bậc anh hùng!", 23, "Truyền Âm Các");
					var parcelVacancy2 = value3.GetParcelVacancy(value3);
					if (parcelVacancy2 != -1)
					{
						value3.AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008001837), parcelVacancy2, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 5);
					}
					else
					{
						value3.HeThongNhacNho("Hành trang không còn chỗ trống!", 10, "Thiên cơ các");
					}
				}
				value3.DCH_HP_BONUS = 0;
				value3.DCH_ATTACK_BONUS = 0;
				value3.DCH_DEF_BONUS = 0;
				value3.DCH_CLVC_BONUS = 0.0;
				value3.DCH_ULPT_BONUS = 0.0;
				value3.DCH_StackC = 0;
				value3.DCH_StackB = 0;
				value3.DCH_StackA = 0;
				value3.DCH_StackA_SoLuong = 0;
				value3.DCH_StackB_SoLuong = 0;
				value3.DCH_StackC_SoLuong = 0;
				DCH_KET_THUC(value3, World.DCH_ChinhPhai_DiemSo, World.DCH_TaPhai_DiemSo);
				DCH_KET_THUC_REMOVE_ALL(value3);
				if (value3.CharacterPKMode != 0)
				{
					value3.SwitchPkMode(0);
				}
				if (value3.DchPlayerTimer != null)
				{
					value3.DchPlayerTimer.Enabled = false;
					value3.DchPlayerTimer.Close();
					value3.DchPlayerTimer.Dispose();
					value3.DchPlayerTimer = null;
				}
				value3.SaveCharacterData();
				value3.UpdateMartialArtsAndStatus();
				value3.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
				value3.TinhToan_NhanVatCoBan_DuLieu();
			}
			World.DCH_Progress = 7;
			ThoiGian3.Enabled = false;
			ThoiGian3.Close();
			ThoiGian3.Dispose();
			World.DCHPlayers.Clear();
			Thread.Sleep(10);
			Dispose();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "ĐẠI CHIẾN HỒN 66 event lỗi： - " + ex);
		}
	}

	public void Dispose()
	{
		List<NpcClass> list = new();
		foreach (var value in World.NpcEvent_DCH.Values)
		{
			list.Add(value);
		}
		if (list != null)
		{
			foreach (var item in list)
			{
				item.GuiDuLieu_TuVong_MotLanCuaQuaiVat();
			}
			list.Clear();
		}
		World.DCH_ToaDo_TanHinh.Clear();
		World.DCH_Progress = 0;
		World.DCH_MidTime = 0;
		World.DanhSachNhanVat_ThamGia_DCH.Clear();
		if (ThoiGian1 != null)
		{
			ThoiGian1.Enabled = false;
			ThoiGian1.Close();
			ThoiGian1.Dispose();
			ThoiGian1 = null;
		}
		if (ThoiGian2 != null)
		{
			ThoiGian2.Enabled = false;
			ThoiGian2.Close();
			ThoiGian2.Dispose();
			ThoiGian2 = null;
		}
		if (ThoiGian3 != null)
		{
			ThoiGian3.Enabled = false;
			ThoiGian3.Close();
			ThoiGian3.Dispose();
			ThoiGian3 = null;
		}
		if (ThoiGian4 != null)
		{
			ThoiGian4.Enabled = false;
			ThoiGian4.Close();
			ThoiGian4.Dispose();
			ThoiGian4 = null;
		}
		if (ThoiGian5 != null)
		{
			ThoiGian5.Enabled = false;
			ThoiGian5.Close();
			ThoiGian5.Dispose();
			ThoiGian5 = null;
		}
		foreach (var value2 in World.allConnectedChars.Values)
		{
			if (value2.NhanVatToaDo_BanDo == 40101 && !value2.Client.TreoMay)
			{
				value2.DCH_HP_BONUS = 0;
				value2.DCH_ATTACK_BONUS = 0;
				value2.DCH_DEF_BONUS = 0;
				value2.DCH_CLVC_BONUS = 0.0;
				value2.DCH_ULPT_BONUS = 0.0;
				value2.Mobile(420f + RNG.Next(-50, 50), 1740f + RNG.Next(-50, 50), 15f, 101, 1);
			}
		}
		World.dch_event_check = null;
		Remove_Event_NPC();
		Remove_Event_NPC_Cua();
		World.DanhSachNhanVat_ThamGia_DCH.Clear();
	}

	public static void GUI_DI_THE_LUC_CHIEN_THAM_DU_FEED_MANG_BI_KICK_RA_TIN_TUC(Players player)
	{
		var string_ = "AA5536000F2713223000020001000A000000010000000E000000010000000100000000000000000000000000000000000000000000000000000055AA";
		var array = Converter.HexStringToByte(string_);
		if (player.Client != null)
		{
			player.Client.Send_Map_Data(array, array.Length);
		}
	}

	public static void KET_THUC_THE_LUC_CHIEN_NHAC_NHO(Players player)
	{
		var string_ = "AA5536000F2713223000040001000A000000010000000E000000010000000100000000000000000000000000000000000000000000000000000055AA";
		var array = Converter.HexStringToByte(string_);
		if (player.Client != null)
		{
			player.Client.Send_Map_Data(array, array.Length);
		}
	}

	public static void KET_THUC_CHIEN_HON_NHAC_NHO(Players player)
	{
		var string_ = "AA55AC006E0500005301A400060001000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA";
		var array = Converter.HexStringToByte(string_);
		if (player.Client != null)
		{
			player.Client.Send(array, array.Length);
		}
	}

	public void DCH_BATDAU_TIME(Players player, int time)
	{
		var string_ = "AA55AC000F0500005301A40006000A000000010001000000000000000300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA";
		var array = Converter.HexStringToByte(string_);
		System.Buffer.BlockCopy(BitConverter.GetBytes(time), 0, array, 28, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 4);
		if (player.Client != null)
		{
			player.Client.Send(array, array.Length);
		}
	}

	public void Time_Dem_Nguoc(Players player, int int_109)
	{
		try
		{
			var array = Converter.HexStringToByte("AA552E000F2713222000090001000B000000010000000C0000002101000000000000000000000000000000000000000002EE55AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 26, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
			if (player.Client != null)
			{
				player.Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public static void DCH_BATDAU(Players player)
	{
		var string_ = "AA55AC001E0500005301A400060007000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA";
		var array = Converter.HexStringToByte(string_);
		if (player.Client != null)
		{
			player.Client.Send(array, array.Length);
		}
	}

	public static void DCH_UPDATE_DIEMSO(Players player, int DiemBenXanh, int DiemBenDo)
	{
		var string_ = "AA55AC006E0500005301A400060006000000010040771B00000000003C671B0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA";
		var array = Converter.HexStringToByte(string_);
		System.Buffer.BlockCopy(BitConverter.GetBytes(DiemBenXanh), 0, array, 20, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(DiemBenDo), 0, array, 28, 4);
		if (player.Client != null)
		{
			player.Client.Send(array, array.Length);
		}
	}

	public static void DCH_KET_THUC(Players player, int DiemBenXanh, int DiemBenDo)
	{
		var string_ = "AA55AC006E0500005301A400060009000000010040771B000000000093291A0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA";
		var array = Converter.HexStringToByte(string_);
		System.Buffer.BlockCopy(BitConverter.GetBytes(DiemBenXanh), 0, array, 20, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(DiemBenDo), 0, array, 28, 4);
		if (player.Client != null)
		{
			player.Client.Send(array, array.Length);
		}
	}

	public static void DCH_KET_THUC_REMOVE_ALL(Players player)
	{
		var string_ = "AA55AE007F025301A400060002000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA";
		var array = Converter.HexStringToByte(string_);
		System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
		if (player.Client != null)
		{
			player.Client.Send_Map_Data(array, array.Length);
		}
	}

	public void Add_NPC_Cong_DCH(int int_0, float float_0, float float_1, float FACE1, float FACE2, int int_1, int type)
	{
		try
		{
			if (World.MonsterTemplateList.TryGetValue(int_0, out var value))
			{
				NpcClass npcClass = new();
				npcClass.FLD_PID = value.FLD_PID;
				npcClass.Name = value.Name;
				npcClass.Level = value.Level;
				npcClass.Rxjh_Exp = value.Rxjh_Exp;
				npcClass.Rxjh_X = float_0;
				npcClass.Rxjh_Y = float_1;
				npcClass.Rxjh_Z = 15f;
				npcClass.Rxjh_cs_X = float_0;
				npcClass.Rxjh_cs_Y = float_1;
				npcClass.Rxjh_cs_Z = 15f;
				npcClass.Rxjh_Map = int_1;
				npcClass.IsNpc = 0;
				npcClass.FLD_FACE1 = FACE1;
				npcClass.FLD_FACE2 = FACE2;
				npcClass.Max_Rxjh_HP = value.Rxjh_HP;
				npcClass.Rxjh_HP = value.Rxjh_HP;
				npcClass.FLD_AT = value.FLD_AT;
				npcClass.FLD_DF = value.FLD_DF;
				npcClass.FLD_AUTO = 0;
				npcClass.FLD_BOSS = 0;
				npcClass.FLD_NEWTIME = 10;
				npcClass.QuaiXuatHien_DuyNhatMotLan = true;
				npcClass.timeNpc_HoiSinh = DateTime.MinValue;
				npcClass.DCH_Tower = type;
				if (World.MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
				{
					value2.add(npcClass);
				}
				else
				{
					MapClass mapClass = new();
					mapClass.MapID = npcClass.Rxjh_Map;
					mapClass.add(npcClass);
					World.MapList.Add(mapClass.MapID, mapClass);
				}
				npcClass.GetRange_PlayersSendIncreaseQuantityPackage();
				World.NpcEvent_DCH.Add(npcClass.NPC_SessionID, npcClass);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Add NPC số lượng - lỗi 44 [" + int_0 + "]error：" + ex);
		}
	}

	public void Add_NPC_Tru_TaPhai_DCH(int int_0, float float_0, float float_1, float FACE1, float FACE2, int int_1, int type)
	{
		try
		{
			if (World.MonsterTemplateList.TryGetValue(int_0, out var value))
			{
				NpcClass npcClass = new();
				npcClass.FLD_PID = value.FLD_PID;
				npcClass.Name = value.Name;
				npcClass.Level = value.Level;
				npcClass.Rxjh_Exp = value.Rxjh_Exp;
				npcClass.Rxjh_X = float_0;
				npcClass.Rxjh_Y = float_1;
				npcClass.Rxjh_Z = 15f;
				npcClass.Rxjh_cs_X = float_0;
				npcClass.Rxjh_cs_Y = float_1;
				npcClass.Rxjh_cs_Z = 15f;
				npcClass.Rxjh_Map = int_1;
				npcClass.IsNpc = 0;
				npcClass.FLD_FACE1 = 0f;
				npcClass.FLD_FACE2 = 0f;
				npcClass.Max_Rxjh_HP = value.Rxjh_HP;
				npcClass.Rxjh_HP = value.Rxjh_HP;
				npcClass.FLD_AT = value.FLD_AT;
				npcClass.FLD_DF = value.FLD_DF;
				npcClass.FLD_AUTO = 0;
				npcClass.FLD_BOSS = 0;
				npcClass.FLD_NEWTIME = 10;
				npcClass.QuaiXuatHien_DuyNhatMotLan = false;
				npcClass.timeNpc_HoiSinh = DateTime.MinValue;
				npcClass.DCH_Tower = type;
				if (World.MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
				{
					value2.add(npcClass);
				}
				else
				{
					MapClass mapClass = new();
					mapClass.MapID = npcClass.Rxjh_Map;
					mapClass.add(npcClass);
					World.MapList.Add(mapClass.MapID, mapClass);
				}
				npcClass.GetRange_PlayersSendIncreaseQuantityPackage();
				World.NpcEvent_DCH.Add(npcClass.NPC_SessionID, npcClass);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Add NPC DCH số lượng - lỗi 44 [" + int_0 + "]error：" + ex);
		}
	}

	public void Add_NPC_Tru_9_Phai_DCH(int int_0, float float_0, float float_1, float FACE1, float FACE2, int int_1, int type)
	{
		try
		{
			if (World.MonsterTemplateList.TryGetValue(int_0, out var value))
			{
				NpcClass npcClass = new();
				npcClass.FLD_PID = value.FLD_PID;
				npcClass.Name = value.Name;
				npcClass.Level = value.Level;
				npcClass.Rxjh_Exp = value.Rxjh_Exp;
				npcClass.Rxjh_X = float_0;
				npcClass.Rxjh_Y = float_1;
				npcClass.Rxjh_Z = 15f;
				npcClass.Rxjh_cs_X = float_0;
				npcClass.Rxjh_cs_Y = float_1;
				npcClass.Rxjh_cs_Z = 15f;
				npcClass.Rxjh_Map = int_1;
				npcClass.IsNpc = 0;
				npcClass.FLD_FACE1 = 0f;
				npcClass.FLD_FACE2 = 1f;
				npcClass.Max_Rxjh_HP = value.Rxjh_HP;
				npcClass.Rxjh_HP = value.Rxjh_HP;
				npcClass.FLD_AT = value.FLD_AT;
				npcClass.FLD_DF = value.FLD_DF;
				npcClass.FLD_AUTO = 0;
				npcClass.FLD_BOSS = 0;
				npcClass.FLD_NEWTIME = 10;
				npcClass.QuaiXuatHien_DuyNhatMotLan = false;
				npcClass.timeNpc_HoiSinh = DateTime.MinValue;
				npcClass.DCH_Tower = type;
				if (World.MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
				{
					value2.add(npcClass);
				}
				else
				{
					MapClass mapClass = new();
					mapClass.MapID = npcClass.Rxjh_Map;
					mapClass.add(npcClass);
					World.MapList.Add(mapClass.MapID, mapClass);
				}
				npcClass.GetRange_PlayersSendIncreaseQuantityPackage();
				World.NpcEvent_DCH.Add(npcClass.NPC_SessionID, npcClass);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Add NPC DCH số lượng - lỗi 44444 [" + int_0 + "] error： " + ex);
		}
	}

	public void Add_Boss_DCH_Random(int ID_NPC, int time_hoisinh, int Rxjh_HP, int Map)
	{
		try
		{
			if (World.MonsterTemplateList.TryGetValue(ID_NPC, out var value))
			{
				float num = RNG.Next(-250, 200);
				NpcClass npcClass = new();
				npcClass.FLD_PID = value.FLD_PID;
				npcClass.Name = value.Name;
				npcClass.Level = value.Level;
				npcClass.Rxjh_Exp = value.Rxjh_Exp;
				npcClass.Rxjh_X = num;
				npcClass.Rxjh_Y = 0f;
				npcClass.Rxjh_Z = 15f;
				npcClass.Rxjh_cs_X = num;
				npcClass.Rxjh_cs_Y = 0f;
				npcClass.Rxjh_cs_Z = 15f;
				npcClass.Rxjh_Map = Map;
				npcClass.IsNpc = 0;
				npcClass.FLD_FACE1 = 0f;
				npcClass.FLD_FACE2 = 0f;
				npcClass.Max_Rxjh_HP = Rxjh_HP;
				npcClass.Rxjh_HP = Rxjh_HP;
				npcClass.FLD_AT = value.FLD_AT;
				npcClass.FLD_DF = value.FLD_DF;
				npcClass.FLD_AUTO = value.FLD_AUTO;
				npcClass.FLD_BOSS = 0;
				npcClass.FLD_NEWTIME = time_hoisinh;
				npcClass.QuaiXuatHien_DuyNhatMotLan = false;
				npcClass.timeNpc_HoiSinh = DateTime.MinValue;
				if (World.MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
				{
					value2.add(npcClass);
				}
				else
				{
					MapClass mapClass = new();
					mapClass.MapID = npcClass.Rxjh_Map;
					mapClass.add(npcClass);
					World.MapList.Add(mapClass.MapID, mapClass);
				}
				npcClass.GetRange_PlayersSendIncreaseQuantityPackage();
				World.NpcEvent_DCH.Add(npcClass.NPC_SessionID, npcClass);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Add NPC số lượng - lỗi 4455 - " + ex);
		}
	}

	public void Add_Quai_Random_SL(int int_0, float float_0, float float_1, int int_1, int soluong)
	{
		try
		{
			for (var i = 0; i < soluong; i++)
			{
				var num = RNG.Next((int)float_0 - 15, (int)float_0 + 15);
				var num2 = RNG.Next((int)float_1 - 15, (int)float_1 + 15);
				if (World.MonsterTemplateList.TryGetValue(int_0, out var value))
				{
					NpcClass npcClass = new();
					npcClass.FLD_PID = value.FLD_PID;
					npcClass.Name = value.Name;
					npcClass.Level = value.Level;
					npcClass.Rxjh_Exp = value.Rxjh_Exp;
					npcClass.Rxjh_X = num;
					npcClass.Rxjh_Y = num2;
					npcClass.Rxjh_Z = 15f;
					npcClass.Rxjh_cs_X = num;
					npcClass.Rxjh_cs_Y = num2;
					npcClass.Rxjh_cs_Z = 15f;
					npcClass.Rxjh_Map = int_1;
					npcClass.IsNpc = 0;
					npcClass.FLD_FACE1 = RNG.Next(-1, 1);
					npcClass.FLD_FACE2 = RNG.Next(-1, 1);
					npcClass.Max_Rxjh_HP = value.Rxjh_HP;
					npcClass.Rxjh_HP = value.Rxjh_HP;
					npcClass.FLD_AT = value.FLD_AT;
					npcClass.FLD_DF = value.FLD_DF;
					npcClass.FLD_AUTO = value.FLD_AUTO;
					npcClass.FLD_BOSS = 0;
					npcClass.FLD_NEWTIME = 10;
					npcClass.QuaiXuatHien_DuyNhatMotLan = false;
					npcClass.timeNpc_HoiSinh = DateTime.MinValue;
					if (World.MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
					{
						value2.add(npcClass);
					}
					else
					{
						MapClass mapClass = new();
						mapClass.MapID = npcClass.Rxjh_Map;
						mapClass.add(npcClass);
						World.MapList.Add(mapClass.MapID, mapClass);
					}
					npcClass.GetRange_PlayersSendIncreaseQuantityPackage();
					World.NpcEvent_DCH.Add(npcClass.NPC_SessionID, npcClass);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Add NPC số lượng - lỗi 445566 [" + int_0 + "] error： " + ex);
		}
	}

	public void Remove_Event_NPC()
	{
		try
		{
			World.delNpc(40101, 15881);
			World.delNpc(40101, 15882);
			World.delNpc(40101, 16271);
			World.delNpc(40101, 16270);
		}
		catch
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Debug, "---------- Xóa NPC DCH lỗi !! ----------");
		}
	}

	public void Remove_Event_NPC_Cua()
	{
		try
		{
			List<NpcClass> list = new();
			foreach (var value in World.NpcEvent_DCH.Values)
			{
				if (value.FLD_PID == 15881 || value.FLD_PID == 15882)
				{
					list.Add(value);
				}
			}
			if (list == null)
			{
				return;
			}
			foreach (var item in list)
			{
				item.GuiDuLieu_TuVong_MotLanCuaQuaiVat();
				World.NpcEvent_DCH.Remove(item.NPC_SessionID);
			}
			list.Clear();
		}
		catch
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Debug, "Xoa Cổng DCH lỗi !! ----------");
		}
	}

	public void Install_Event_Monster_va_Boss()
	{
		try
		{
			Add_Quai_Random_SL(16272, -188f, -68f, 40101, 2);
			Add_Quai_Random_SL(16272, -109f, -89f, 40101, 2);
			Add_Quai_Random_SL(16272, 245f, 89f, 40101, 2);
			Add_Quai_Random_SL(16272, 144f, 89f, 40101, 2);
			Add_Quai_Random_SL(16273, 116f, -95f, 40101, 2);
			Add_Quai_Random_SL(16273, 238f, -93f, 40101, 2);
			Add_Quai_Random_SL(16273, -95f, 98f, 40101, 2);
			Add_Quai_Random_SL(16273, -200f, 111f, 40101, 2);
			Add_Quai_Random_SL(16276, -341f, 836f, 40101, 2);
			Add_Quai_Random_SL(16276, 354f, -828f, 40101, 2);
			Add_Quai_Random_SL(16277, 351f, 820f, 40101, 1);
			Add_Quai_Random_SL(16277, -341f, -826f, 40101, 1);
			Add_Quai_Random_SL(16275, -346f, 8f, 40101, 4);
			Add_Quai_Random_SL(16274, 352f, 8f, 40101, 4);
			Add_Boss_DCH_Random(16278, 60, 5000000, 40101);
		}
		catch
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Add NPC DCH lỗi !! ----------");
		}
	}

	public void Install_Event_Cong_va_Tru()
	{
		try
		{
			Add_NPC_Cong_DCH(15881, -256f, -477f, 1f, 0f, 40101, 0);
			Add_NPC_Cong_DCH(15881, 262f, -483f, -1f, 0f, 40101, 0);
			Add_NPC_Cong_DCH(15881, -6f, -752f, 0f, 1f, 40101, 0);
			Add_NPC_Cong_DCH(15881, 0f, -222f, 0f, 0f, 40101, 0);
			Add_NPC_Cong_DCH(15882, -247f, 476f, 1f, 0f, 40101, 0);
			Add_NPC_Cong_DCH(15882, 276f, 487f, -1f, 0f, 40101, 0);
			Add_NPC_Cong_DCH(15882, 16f, 733f, 0f, 0f, 40101, 0);
			Add_NPC_Cong_DCH(15882, 1f, 216f, 0f, 1f, 40101, 0);
			Add_NPC_Tru_9_Phai_DCH(16270, -9f, -483f, 0f, 1f, 40101, 1);
			Add_NPC_Tru_TaPhai_DCH(16271, 9f, 483f, 0f, 0f, 40101, 2);
		}
		catch
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Add NPC DCH lỗi !! ----------");
		}
	}

	public void SetUp_ToaDoTanHinh_1()
	{
		var rxjh_Map = 40101;
		var rxjh_Z = 15f;
		X_Toa_Do_Class item = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 9f,
			Rxjh_Y = 485f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item);
		X_Toa_Do_Class item2 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -7f,
			Rxjh_Y = -486f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item2);
		X_Toa_Do_Class item3 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 271f,
			Rxjh_Y = 49f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item3);
		X_Toa_Do_Class item4 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 245f,
			Rxjh_Y = 112f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item4);
		X_Toa_Do_Class item5 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 252f,
			Rxjh_Y = 158f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item5);
		X_Toa_Do_Class item6 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 215f,
			Rxjh_Y = 80f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item6);
		X_Toa_Do_Class item7 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 203f,
			Rxjh_Y = 38f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item7);
		X_Toa_Do_Class item8 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 171f,
			Rxjh_Y = 106f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item8);
		X_Toa_Do_Class item9 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 167f,
			Rxjh_Y = 135f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item9);
		X_Toa_Do_Class item10 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 75f,
			Rxjh_Y = 168f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item10);
		X_Toa_Do_Class item11 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 74f,
			Rxjh_Y = 131f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item11);
		X_Toa_Do_Class item12 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 114f,
			Rxjh_Y = 87f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item12);
		X_Toa_Do_Class item13 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 111f,
			Rxjh_Y = 37f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item13);
		X_Toa_Do_Class item14 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 136f,
			Rxjh_Y = 43f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item14);
		X_Toa_Do_Class item15 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 205f,
			Rxjh_Y = 40f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item15);
		X_Toa_Do_Class item16 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 217f,
			Rxjh_Y = 72f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item16);
		X_Toa_Do_Class item17 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -78f,
			Rxjh_Y = 168f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item17);
		X_Toa_Do_Class item18 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -80f,
			Rxjh_Y = 132f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item18);
		X_Toa_Do_Class item19 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -107f,
			Rxjh_Y = 82f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item19);
		X_Toa_Do_Class item20 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -105f,
			Rxjh_Y = 56f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item20);
		X_Toa_Do_Class item21 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -84f,
			Rxjh_Y = 48f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item21);
		X_Toa_Do_Class item22 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -164f,
			Rxjh_Y = 129f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item22);
		X_Toa_Do_Class item23 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -178f,
			Rxjh_Y = 85f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item23);
		X_Toa_Do_Class item24 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -165f,
			Rxjh_Y = 54f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item24);
		X_Toa_Do_Class item25 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -186f,
			Rxjh_Y = 47f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item25);
		X_Toa_Do_Class item26 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -246f,
			Rxjh_Y = 62f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item26);
		X_Toa_Do_Class item27 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -246f,
			Rxjh_Y = 87f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item27);
		X_Toa_Do_Class item28 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -238f,
			Rxjh_Y = 148f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item28);
		X_Toa_Do_Class item29 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 78f,
			Rxjh_Y = -171f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item29);
		X_Toa_Do_Class item30 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 77f,
			Rxjh_Y = -134f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item30);
		X_Toa_Do_Class item31 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 139f,
			Rxjh_Y = -128f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item31);
		X_Toa_Do_Class item32 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 137f,
			Rxjh_Y = -78f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item32);
		X_Toa_Do_Class item33 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 111f,
			Rxjh_Y = -58f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item33);
		X_Toa_Do_Class item34 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 137f,
			Rxjh_Y = -61f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item34);
		X_Toa_Do_Class item35 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 141f,
			Rxjh_Y = -79f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item35);
		X_Toa_Do_Class item36 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 194f,
			Rxjh_Y = -40f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item36);
		X_Toa_Do_Class item37 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 215f,
			Rxjh_Y = 80f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item37);
		X_Toa_Do_Class item38 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 257f,
			Rxjh_Y = -53f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item38);
		X_Toa_Do_Class item39 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 279f,
			Rxjh_Y = -60f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item39);
		X_Toa_Do_Class item40 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 287f,
			Rxjh_Y = -124f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item40);
		X_Toa_Do_Class item41 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 234f,
			Rxjh_Y = -118f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item41);
		X_Toa_Do_Class item42 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 289f,
			Rxjh_Y = -122f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item42);
		X_Toa_Do_Class item43 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -78f,
			Rxjh_Y = -169f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item43);
		X_Toa_Do_Class item44 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -74f,
			Rxjh_Y = -134f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item44);
		X_Toa_Do_Class item45 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -86f,
			Rxjh_Y = -40f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item45);
		X_Toa_Do_Class item46 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -92f,
			Rxjh_Y = -76f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item46);
		X_Toa_Do_Class item47 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -107f,
			Rxjh_Y = -93f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item47);
		X_Toa_Do_Class item48 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -114f,
			Rxjh_Y = -46f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item48);
		X_Toa_Do_Class item49 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -99f,
			Rxjh_Y = -80f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item49);
		X_Toa_Do_Class item50 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -153f,
			Rxjh_Y = -94f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item50);
		X_Toa_Do_Class item51 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -163f,
			Rxjh_Y = -127f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item51);
		X_Toa_Do_Class item52 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -175f,
			Rxjh_Y = -44f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item52);
		X_Toa_Do_Class item53 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -202f,
			Rxjh_Y = -47f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item53);
		X_Toa_Do_Class item54 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -228f,
			Rxjh_Y = -82f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item54);
		X_Toa_Do_Class item55 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -226f,
			Rxjh_Y = -131f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item55);
		X_Toa_Do_Class item56 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -289f,
			Rxjh_Y = -296f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item56);
		X_Toa_Do_Class item57 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -289f,
			Rxjh_Y = -348f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item57);
		X_Toa_Do_Class item58 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -296f,
			Rxjh_Y = -615f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item58);
		X_Toa_Do_Class item59 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -296f,
			Rxjh_Y = -657f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item59);
		X_Toa_Do_Class item60 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -188f,
			Rxjh_Y = -797f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item60);
		X_Toa_Do_Class item61 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -126f,
			Rxjh_Y = -797f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item61);
		X_Toa_Do_Class item62 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -126f,
			Rxjh_Y = -797f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item62);
		X_Toa_Do_Class item63 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 117f,
			Rxjh_Y = -807f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item63);
		X_Toa_Do_Class item64 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 163f,
			Rxjh_Y = -807f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item64);
		X_Toa_Do_Class item65 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 319f,
			Rxjh_Y = -674f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item65);
		X_Toa_Do_Class item66 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 319f,
			Rxjh_Y = -620f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item66);
		X_Toa_Do_Class item67 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 325f,
			Rxjh_Y = -353f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item67);
		X_Toa_Do_Class item68 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 306f,
			Rxjh_Y = 298f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item68);
		X_Toa_Do_Class item69 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 308f,
			Rxjh_Y = 358f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item69);
		X_Toa_Do_Class item70 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 307f,
			Rxjh_Y = 330f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item70);
		X_Toa_Do_Class item71 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 318f,
			Rxjh_Y = 606f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item71);
		X_Toa_Do_Class item72 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 320f,
			Rxjh_Y = 649f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item72);
		X_Toa_Do_Class item73 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 174f,
			Rxjh_Y = 779f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item73);
		X_Toa_Do_Class item74 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 138f,
			Rxjh_Y = 779f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item74);
		X_Toa_Do_Class item75 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -95f,
			Rxjh_Y = 786f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item75);
		X_Toa_Do_Class item76 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -154f,
			Rxjh_Y = 786f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item76);
		X_Toa_Do_Class item77 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -281f,
			Rxjh_Y = 649f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item77);
		X_Toa_Do_Class item78 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -283f,
			Rxjh_Y = 609f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item78);
		X_Toa_Do_Class item79 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -295f,
			Rxjh_Y = 297f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item79);
		X_Toa_Do_Class item80 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -296f,
			Rxjh_Y = 323f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item80);
		X_Toa_Do_Class item81 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -301f,
			Rxjh_Y = 351f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item81);
		X_Toa_Do_Class item82 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -2f,
			Rxjh_Y = -492f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item82);
		X_Toa_Do_Class item83 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -18f,
			Rxjh_Y = -492f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item83);
		X_Toa_Do_Class item84 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = 18f,
			Rxjh_Y = 492f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item84);
		X_Toa_Do_Class item85 = new()
		{
			Rxjh_Map = rxjh_Map,
			Rxjh_X = -2f,
			Rxjh_Y = 492f,
			Rxjh_Z = rxjh_Z
		};
		World.DCH_ToaDo_TanHinh.Add(item85);
	}

	public void SetUp_ToaDoTanHinh()
	{
		X_Toa_Do_Class x_Toa_Do_Class = new();
		x_Toa_Do_Class.Rxjh_Map = 40101;
		x_Toa_Do_Class.Rxjh_X = -24f;
		x_Toa_Do_Class.Rxjh_Y = -491f;
		x_Toa_Do_Class.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class);
		X_Toa_Do_Class x_Toa_Do_Class2 = new();
		x_Toa_Do_Class2.Rxjh_Map = 40101;
		x_Toa_Do_Class2.Rxjh_X = -28f;
		x_Toa_Do_Class2.Rxjh_Y = -474f;
		x_Toa_Do_Class2.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class2);
		X_Toa_Do_Class x_Toa_Do_Class3 = new();
		x_Toa_Do_Class3.Rxjh_Map = 40101;
		x_Toa_Do_Class3.Rxjh_X = -20f;
		x_Toa_Do_Class3.Rxjh_Y = -495f;
		x_Toa_Do_Class3.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class3);
		X_Toa_Do_Class x_Toa_Do_Class4 = new();
		x_Toa_Do_Class4.Rxjh_Map = 40101;
		x_Toa_Do_Class4.Rxjh_X = -15f;
		x_Toa_Do_Class4.Rxjh_Y = -500f;
		x_Toa_Do_Class4.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class4);
		X_Toa_Do_Class x_Toa_Do_Class5 = new();
		x_Toa_Do_Class5.Rxjh_Map = 40101;
		x_Toa_Do_Class5.Rxjh_X = -10f;
		x_Toa_Do_Class5.Rxjh_Y = -502f;
		x_Toa_Do_Class5.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class5);
		X_Toa_Do_Class x_Toa_Do_Class6 = new();
		x_Toa_Do_Class6.Rxjh_Map = 40101;
		x_Toa_Do_Class6.Rxjh_X = -5f;
		x_Toa_Do_Class6.Rxjh_Y = -502f;
		x_Toa_Do_Class6.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class6);
		X_Toa_Do_Class x_Toa_Do_Class7 = new();
		x_Toa_Do_Class7.Rxjh_Map = 40101;
		x_Toa_Do_Class7.Rxjh_X = 0f;
		x_Toa_Do_Class7.Rxjh_Y = -502f;
		x_Toa_Do_Class7.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class7);
		X_Toa_Do_Class x_Toa_Do_Class8 = new();
		x_Toa_Do_Class8.Rxjh_Map = 40101;
		x_Toa_Do_Class8.Rxjh_X = 0f;
		x_Toa_Do_Class8.Rxjh_Y = -502f;
		x_Toa_Do_Class8.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class8);
		X_Toa_Do_Class x_Toa_Do_Class9 = new();
		x_Toa_Do_Class9.Rxjh_Map = 40101;
		x_Toa_Do_Class9.Rxjh_X = 7f;
		x_Toa_Do_Class9.Rxjh_Y = -497f;
		x_Toa_Do_Class9.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class9);
		X_Toa_Do_Class x_Toa_Do_Class10 = new();
		x_Toa_Do_Class10.Rxjh_Map = 40101;
		x_Toa_Do_Class10.Rxjh_X = 9f;
		x_Toa_Do_Class10.Rxjh_Y = -485f;
		x_Toa_Do_Class10.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class10);
		X_Toa_Do_Class x_Toa_Do_Class11 = new();
		x_Toa_Do_Class11.Rxjh_Map = 40101;
		x_Toa_Do_Class11.Rxjh_X = 6f;
		x_Toa_Do_Class11.Rxjh_Y = -480f;
		x_Toa_Do_Class11.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class11);
		X_Toa_Do_Class x_Toa_Do_Class12 = new();
		x_Toa_Do_Class12.Rxjh_Map = 40101;
		x_Toa_Do_Class12.Rxjh_X = -309f;
		x_Toa_Do_Class12.Rxjh_Y = -591f;
		x_Toa_Do_Class12.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class12);
		X_Toa_Do_Class x_Toa_Do_Class13 = new();
		x_Toa_Do_Class13.Rxjh_Map = 40101;
		x_Toa_Do_Class13.Rxjh_X = -308f;
		x_Toa_Do_Class13.Rxjh_Y = -617f;
		x_Toa_Do_Class13.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class13);
		X_Toa_Do_Class x_Toa_Do_Class14 = new();
		x_Toa_Do_Class14.Rxjh_Map = 40101;
		x_Toa_Do_Class14.Rxjh_X = -311f;
		x_Toa_Do_Class14.Rxjh_Y = -637f;
		x_Toa_Do_Class14.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class13);
		X_Toa_Do_Class x_Toa_Do_Class15 = new();
		x_Toa_Do_Class15.Rxjh_Map = 40101;
		x_Toa_Do_Class15.Rxjh_X = -309f;
		x_Toa_Do_Class15.Rxjh_Y = -656f;
		x_Toa_Do_Class15.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class15);
		X_Toa_Do_Class x_Toa_Do_Class16 = new();
		x_Toa_Do_Class16.Rxjh_Map = 40101;
		x_Toa_Do_Class16.Rxjh_X = -308f;
		x_Toa_Do_Class16.Rxjh_Y = -676f;
		x_Toa_Do_Class16.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class16);
		X_Toa_Do_Class x_Toa_Do_Class17 = new();
		x_Toa_Do_Class17.Rxjh_Map = 40101;
		x_Toa_Do_Class17.Rxjh_X = -283f;
		x_Toa_Do_Class17.Rxjh_Y = -670f;
		x_Toa_Do_Class17.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class17);
		X_Toa_Do_Class x_Toa_Do_Class18 = new();
		x_Toa_Do_Class18.Rxjh_Map = 40101;
		x_Toa_Do_Class18.Rxjh_X = -312f;
		x_Toa_Do_Class18.Rxjh_Y = -638f;
		x_Toa_Do_Class18.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class18);
		X_Toa_Do_Class x_Toa_Do_Class19 = new();
		x_Toa_Do_Class19.Rxjh_Map = 40101;
		x_Toa_Do_Class19.Rxjh_X = -299f;
		x_Toa_Do_Class19.Rxjh_Y = -608f;
		x_Toa_Do_Class19.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class19);
		X_Toa_Do_Class x_Toa_Do_Class20 = new();
		x_Toa_Do_Class20.Rxjh_Map = 40101;
		x_Toa_Do_Class20.Rxjh_X = -297f;
		x_Toa_Do_Class20.Rxjh_Y = -597f;
		x_Toa_Do_Class20.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class20);
		X_Toa_Do_Class x_Toa_Do_Class21 = new();
		x_Toa_Do_Class21.Rxjh_Map = 40101;
		x_Toa_Do_Class21.Rxjh_X = -287f;
		x_Toa_Do_Class21.Rxjh_Y = -597f;
		x_Toa_Do_Class21.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class21);
		X_Toa_Do_Class x_Toa_Do_Class22 = new();
		x_Toa_Do_Class22.Rxjh_Map = 40101;
		x_Toa_Do_Class22.Rxjh_X = -281f;
		x_Toa_Do_Class22.Rxjh_Y = -592f;
		x_Toa_Do_Class22.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class22);
		X_Toa_Do_Class x_Toa_Do_Class23 = new();
		x_Toa_Do_Class23.Rxjh_Map = 40101;
		x_Toa_Do_Class23.Rxjh_X = -279f;
		x_Toa_Do_Class23.Rxjh_Y = -600f;
		x_Toa_Do_Class23.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class23);
		X_Toa_Do_Class x_Toa_Do_Class24 = new();
		x_Toa_Do_Class24.Rxjh_Map = 40101;
		x_Toa_Do_Class24.Rxjh_X = -284f;
		x_Toa_Do_Class24.Rxjh_Y = -609f;
		x_Toa_Do_Class24.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class24);
		X_Toa_Do_Class x_Toa_Do_Class25 = new();
		x_Toa_Do_Class25.Rxjh_Map = 40101;
		x_Toa_Do_Class25.Rxjh_X = -278f;
		x_Toa_Do_Class25.Rxjh_Y = -619f;
		x_Toa_Do_Class25.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class25);
		X_Toa_Do_Class x_Toa_Do_Class26 = new();
		x_Toa_Do_Class26.Rxjh_Map = 40101;
		x_Toa_Do_Class26.Rxjh_X = -287f;
		x_Toa_Do_Class26.Rxjh_Y = -618f;
		x_Toa_Do_Class26.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class26);
		X_Toa_Do_Class x_Toa_Do_Class27 = new();
		x_Toa_Do_Class27.Rxjh_Map = 40101;
		x_Toa_Do_Class27.Rxjh_X = -298f;
		x_Toa_Do_Class27.Rxjh_Y = -630f;
		x_Toa_Do_Class27.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class27);
		X_Toa_Do_Class x_Toa_Do_Class28 = new();
		x_Toa_Do_Class28.Rxjh_Map = 40101;
		x_Toa_Do_Class28.Rxjh_X = -282f;
		x_Toa_Do_Class28.Rxjh_Y = -645f;
		x_Toa_Do_Class28.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class28);
		X_Toa_Do_Class x_Toa_Do_Class29 = new();
		x_Toa_Do_Class29.Rxjh_Map = 40101;
		x_Toa_Do_Class29.Rxjh_X = -293f;
		x_Toa_Do_Class29.Rxjh_Y = -658f;
		x_Toa_Do_Class29.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class29);
		X_Toa_Do_Class x_Toa_Do_Class30 = new();
		x_Toa_Do_Class30.Rxjh_Map = 40101;
		x_Toa_Do_Class30.Rxjh_X = -296f;
		x_Toa_Do_Class30.Rxjh_Y = -649f;
		x_Toa_Do_Class30.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class30);
		X_Toa_Do_Class x_Toa_Do_Class31 = new();
		x_Toa_Do_Class31.Rxjh_Map = 40101;
		x_Toa_Do_Class31.Rxjh_X = -293f;
		x_Toa_Do_Class31.Rxjh_Y = -682f;
		x_Toa_Do_Class31.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class31);
		X_Toa_Do_Class x_Toa_Do_Class32 = new();
		x_Toa_Do_Class32.Rxjh_Map = 40101;
		x_Toa_Do_Class32.Rxjh_X = -205f;
		x_Toa_Do_Class32.Rxjh_Y = -131f;
		x_Toa_Do_Class32.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class32);
		X_Toa_Do_Class x_Toa_Do_Class33 = new();
		x_Toa_Do_Class33.Rxjh_Map = 40101;
		x_Toa_Do_Class33.Rxjh_X = -214f;
		x_Toa_Do_Class33.Rxjh_Y = -138f;
		x_Toa_Do_Class33.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class33);
		X_Toa_Do_Class x_Toa_Do_Class34 = new();
		x_Toa_Do_Class34.Rxjh_Map = 40101;
		x_Toa_Do_Class34.Rxjh_X = -220f;
		x_Toa_Do_Class34.Rxjh_Y = -145f;
		x_Toa_Do_Class34.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class34);
		X_Toa_Do_Class x_Toa_Do_Class35 = new();
		x_Toa_Do_Class35.Rxjh_Map = 40101;
		x_Toa_Do_Class35.Rxjh_X = -220f;
		x_Toa_Do_Class35.Rxjh_Y = -145f;
		x_Toa_Do_Class35.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class35);
		X_Toa_Do_Class x_Toa_Do_Class36 = new();
		x_Toa_Do_Class36.Rxjh_Map = 40101;
		x_Toa_Do_Class36.Rxjh_X = -236f;
		x_Toa_Do_Class36.Rxjh_Y = -146f;
		x_Toa_Do_Class36.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class36);
		X_Toa_Do_Class x_Toa_Do_Class37 = new();
		x_Toa_Do_Class37.Rxjh_Map = 40101;
		x_Toa_Do_Class37.Rxjh_X = -221f;
		x_Toa_Do_Class37.Rxjh_Y = -132f;
		x_Toa_Do_Class37.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class37);
		X_Toa_Do_Class x_Toa_Do_Class38 = new();
		x_Toa_Do_Class38.Rxjh_Map = 40101;
		x_Toa_Do_Class38.Rxjh_X = -225f;
		x_Toa_Do_Class38.Rxjh_Y = -122f;
		x_Toa_Do_Class38.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class38);
		X_Toa_Do_Class x_Toa_Do_Class39 = new();
		x_Toa_Do_Class39.Rxjh_Map = 40101;
		x_Toa_Do_Class39.Rxjh_X = -225f;
		x_Toa_Do_Class39.Rxjh_Y = -122f;
		x_Toa_Do_Class39.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class39);
		X_Toa_Do_Class x_Toa_Do_Class40 = new();
		x_Toa_Do_Class40.Rxjh_Map = 40101;
		x_Toa_Do_Class40.Rxjh_X = -242f;
		x_Toa_Do_Class40.Rxjh_Y = -123f;
		x_Toa_Do_Class40.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class40);
		X_Toa_Do_Class x_Toa_Do_Class41 = new();
		x_Toa_Do_Class41.Rxjh_Map = 40101;
		x_Toa_Do_Class41.Rxjh_X = -234f;
		x_Toa_Do_Class41.Rxjh_Y = -123f;
		x_Toa_Do_Class41.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class41);
		X_Toa_Do_Class x_Toa_Do_Class42 = new();
		x_Toa_Do_Class42.Rxjh_Map = 40101;
		x_Toa_Do_Class42.Rxjh_X = -238f;
		x_Toa_Do_Class42.Rxjh_Y = -110f;
		x_Toa_Do_Class42.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class42);
		X_Toa_Do_Class x_Toa_Do_Class43 = new();
		x_Toa_Do_Class43.Rxjh_Map = 40101;
		x_Toa_Do_Class43.Rxjh_X = -246f;
		x_Toa_Do_Class43.Rxjh_Y = -104f;
		x_Toa_Do_Class43.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class43);
		X_Toa_Do_Class x_Toa_Do_Class44 = new();
		x_Toa_Do_Class44.Rxjh_Map = 40101;
		x_Toa_Do_Class44.Rxjh_X = -238f;
		x_Toa_Do_Class44.Rxjh_Y = -103f;
		x_Toa_Do_Class44.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class44);
		X_Toa_Do_Class x_Toa_Do_Class45 = new();
		x_Toa_Do_Class45.Rxjh_Map = 40101;
		x_Toa_Do_Class45.Rxjh_X = -241f;
		x_Toa_Do_Class45.Rxjh_Y = -91f;
		x_Toa_Do_Class45.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class45);
		X_Toa_Do_Class x_Toa_Do_Class46 = new();
		x_Toa_Do_Class46.Rxjh_Map = 40101;
		x_Toa_Do_Class46.Rxjh_X = -220f;
		x_Toa_Do_Class46.Rxjh_Y = -145f;
		x_Toa_Do_Class46.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class46);
		X_Toa_Do_Class x_Toa_Do_Class47 = new();
		x_Toa_Do_Class47.Rxjh_Map = 40101;
		x_Toa_Do_Class47.Rxjh_X = -242f;
		x_Toa_Do_Class47.Rxjh_Y = -77f;
		x_Toa_Do_Class47.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class47);
		X_Toa_Do_Class x_Toa_Do_Class48 = new();
		x_Toa_Do_Class48.Rxjh_Map = 40101;
		x_Toa_Do_Class48.Rxjh_X = -239f;
		x_Toa_Do_Class48.Rxjh_Y = -67f;
		x_Toa_Do_Class48.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class48);
		X_Toa_Do_Class x_Toa_Do_Class49 = new();
		x_Toa_Do_Class49.Rxjh_Map = 40101;
		x_Toa_Do_Class49.Rxjh_X = -231f;
		x_Toa_Do_Class49.Rxjh_Y = -79f;
		x_Toa_Do_Class49.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class49);
		X_Toa_Do_Class x_Toa_Do_Class50 = new();
		x_Toa_Do_Class50.Rxjh_Map = 40101;
		x_Toa_Do_Class50.Rxjh_X = -235f;
		x_Toa_Do_Class50.Rxjh_Y = -80f;
		x_Toa_Do_Class50.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class50);
		X_Toa_Do_Class x_Toa_Do_Class51 = new();
		x_Toa_Do_Class51.Rxjh_Map = 40101;
		x_Toa_Do_Class51.Rxjh_X = -238f;
		x_Toa_Do_Class51.Rxjh_Y = -63f;
		x_Toa_Do_Class51.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class51);
		X_Toa_Do_Class x_Toa_Do_Class52 = new();
		x_Toa_Do_Class52.Rxjh_Map = 40101;
		x_Toa_Do_Class52.Rxjh_X = -216f;
		x_Toa_Do_Class52.Rxjh_Y = -64f;
		x_Toa_Do_Class52.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class52);
		X_Toa_Do_Class x_Toa_Do_Class53 = new();
		x_Toa_Do_Class53.Rxjh_Map = 40101;
		x_Toa_Do_Class53.Rxjh_X = -219f;
		x_Toa_Do_Class53.Rxjh_Y = -76f;
		x_Toa_Do_Class53.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class53);
		X_Toa_Do_Class x_Toa_Do_Class54 = new();
		x_Toa_Do_Class54.Rxjh_Map = 40101;
		x_Toa_Do_Class54.Rxjh_X = -211f;
		x_Toa_Do_Class54.Rxjh_Y = -81f;
		x_Toa_Do_Class54.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class54);
		X_Toa_Do_Class x_Toa_Do_Class55 = new();
		x_Toa_Do_Class55.Rxjh_Map = 40101;
		x_Toa_Do_Class55.Rxjh_X = -221f;
		x_Toa_Do_Class55.Rxjh_Y = -64f;
		x_Toa_Do_Class55.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class55);
		X_Toa_Do_Class x_Toa_Do_Class56 = new();
		x_Toa_Do_Class56.Rxjh_Map = 40101;
		x_Toa_Do_Class56.Rxjh_X = -213f;
		x_Toa_Do_Class56.Rxjh_Y = -79f;
		x_Toa_Do_Class56.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class56);
		X_Toa_Do_Class x_Toa_Do_Class57 = new();
		x_Toa_Do_Class57.Rxjh_Map = 40101;
		x_Toa_Do_Class57.Rxjh_X = -199f;
		x_Toa_Do_Class57.Rxjh_Y = -68f;
		x_Toa_Do_Class57.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class57);
		X_Toa_Do_Class x_Toa_Do_Class58 = new();
		x_Toa_Do_Class58.Rxjh_Map = 40101;
		x_Toa_Do_Class58.Rxjh_X = -186f;
		x_Toa_Do_Class58.Rxjh_Y = -69f;
		x_Toa_Do_Class58.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class58);
		X_Toa_Do_Class x_Toa_Do_Class59 = new();
		x_Toa_Do_Class59.Rxjh_Map = 40101;
		x_Toa_Do_Class59.Rxjh_X = -189f;
		x_Toa_Do_Class59.Rxjh_Y = -57f;
		x_Toa_Do_Class59.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class59);
		X_Toa_Do_Class x_Toa_Do_Class60 = new();
		x_Toa_Do_Class60.Rxjh_Map = 40101;
		x_Toa_Do_Class60.Rxjh_X = -208f;
		x_Toa_Do_Class60.Rxjh_Y = -49f;
		x_Toa_Do_Class60.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class60);
		X_Toa_Do_Class x_Toa_Do_Class61 = new();
		x_Toa_Do_Class61.Rxjh_Map = 40101;
		x_Toa_Do_Class61.Rxjh_X = -205f;
		x_Toa_Do_Class61.Rxjh_Y = -34f;
		x_Toa_Do_Class61.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class61);
		X_Toa_Do_Class x_Toa_Do_Class62 = new();
		x_Toa_Do_Class62.Rxjh_Map = 40101;
		x_Toa_Do_Class62.Rxjh_X = -201f;
		x_Toa_Do_Class62.Rxjh_Y = -26f;
		x_Toa_Do_Class62.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class62);
		X_Toa_Do_Class x_Toa_Do_Class63 = new();
		x_Toa_Do_Class63.Rxjh_Map = 40101;
		x_Toa_Do_Class63.Rxjh_X = -209f;
		x_Toa_Do_Class63.Rxjh_Y = -24f;
		x_Toa_Do_Class63.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class63);
		X_Toa_Do_Class x_Toa_Do_Class64 = new();
		x_Toa_Do_Class64.Rxjh_Map = 40101;
		x_Toa_Do_Class64.Rxjh_X = -221f;
		x_Toa_Do_Class64.Rxjh_Y = -26f;
		x_Toa_Do_Class64.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class64);
		X_Toa_Do_Class x_Toa_Do_Class65 = new();
		x_Toa_Do_Class65.Rxjh_Map = 40101;
		x_Toa_Do_Class65.Rxjh_X = -185f;
		x_Toa_Do_Class65.Rxjh_Y = -41f;
		x_Toa_Do_Class65.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class65);
		X_Toa_Do_Class x_Toa_Do_Class66 = new();
		x_Toa_Do_Class66.Rxjh_Map = 40101;
		x_Toa_Do_Class66.Rxjh_X = -172f;
		x_Toa_Do_Class66.Rxjh_Y = -23f;
		x_Toa_Do_Class66.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class66);
		X_Toa_Do_Class x_Toa_Do_Class67 = new();
		x_Toa_Do_Class67.Rxjh_Map = 40101;
		x_Toa_Do_Class67.Rxjh_X = -173f;
		x_Toa_Do_Class67.Rxjh_Y = -38f;
		x_Toa_Do_Class67.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class67);
		X_Toa_Do_Class x_Toa_Do_Class68 = new();
		x_Toa_Do_Class68.Rxjh_Map = 40101;
		x_Toa_Do_Class68.Rxjh_X = -168f;
		x_Toa_Do_Class68.Rxjh_Y = -53f;
		x_Toa_Do_Class68.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class68);
		X_Toa_Do_Class x_Toa_Do_Class69 = new();
		x_Toa_Do_Class69.Rxjh_Map = 40101;
		x_Toa_Do_Class69.Rxjh_X = -154f;
		x_Toa_Do_Class69.Rxjh_Y = -54f;
		x_Toa_Do_Class69.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class69);
		X_Toa_Do_Class x_Toa_Do_Class70 = new();
		x_Toa_Do_Class70.Rxjh_Map = 40101;
		x_Toa_Do_Class70.Rxjh_X = -131f;
		x_Toa_Do_Class70.Rxjh_Y = -26f;
		x_Toa_Do_Class70.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class70);
		X_Toa_Do_Class x_Toa_Do_Class71 = new();
		x_Toa_Do_Class71.Rxjh_Map = 40101;
		x_Toa_Do_Class71.Rxjh_X = -129f;
		x_Toa_Do_Class71.Rxjh_Y = -34f;
		x_Toa_Do_Class71.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class71);
		X_Toa_Do_Class x_Toa_Do_Class72 = new();
		x_Toa_Do_Class72.Rxjh_Map = 40101;
		x_Toa_Do_Class72.Rxjh_X = -131f;
		x_Toa_Do_Class72.Rxjh_Y = -47f;
		x_Toa_Do_Class72.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class72);
		X_Toa_Do_Class x_Toa_Do_Class73 = new();
		x_Toa_Do_Class73.Rxjh_Map = 40101;
		x_Toa_Do_Class73.Rxjh_X = -121f;
		x_Toa_Do_Class73.Rxjh_Y = -60f;
		x_Toa_Do_Class73.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class73);
		X_Toa_Do_Class x_Toa_Do_Class74 = new();
		x_Toa_Do_Class74.Rxjh_Map = 40101;
		x_Toa_Do_Class74.Rxjh_X = -101f;
		x_Toa_Do_Class74.Rxjh_Y = -64f;
		x_Toa_Do_Class74.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class74);
		X_Toa_Do_Class x_Toa_Do_Class75 = new();
		x_Toa_Do_Class75.Rxjh_Map = 40101;
		x_Toa_Do_Class75.Rxjh_X = -110f;
		x_Toa_Do_Class75.Rxjh_Y = -56f;
		x_Toa_Do_Class75.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class75);
		X_Toa_Do_Class x_Toa_Do_Class76 = new();
		x_Toa_Do_Class35.Rxjh_Map = 40101;
		x_Toa_Do_Class35.Rxjh_X = -97f;
		x_Toa_Do_Class35.Rxjh_Y = -50f;
		x_Toa_Do_Class35.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class35);
		X_Toa_Do_Class x_Toa_Do_Class77 = new();
		x_Toa_Do_Class77.Rxjh_Map = 40101;
		x_Toa_Do_Class77.Rxjh_X = -103f;
		x_Toa_Do_Class77.Rxjh_Y = -39f;
		x_Toa_Do_Class77.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class77);
		X_Toa_Do_Class x_Toa_Do_Class78 = new();
		x_Toa_Do_Class78.Rxjh_Map = 40101;
		x_Toa_Do_Class78.Rxjh_X = -82f;
		x_Toa_Do_Class78.Rxjh_Y = -40f;
		x_Toa_Do_Class78.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class78);
		X_Toa_Do_Class x_Toa_Do_Class79 = new();
		x_Toa_Do_Class79.Rxjh_Map = 40101;
		x_Toa_Do_Class79.Rxjh_X = -92f;
		x_Toa_Do_Class79.Rxjh_Y = -27f;
		x_Toa_Do_Class79.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class79);
		X_Toa_Do_Class x_Toa_Do_Class80 = new();
		x_Toa_Do_Class80.Rxjh_Map = 40101;
		x_Toa_Do_Class80.Rxjh_X = -74f;
		x_Toa_Do_Class80.Rxjh_Y = -29f;
		x_Toa_Do_Class80.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class80);
		X_Toa_Do_Class x_Toa_Do_Class81 = new();
		x_Toa_Do_Class81.Rxjh_Map = 40101;
		x_Toa_Do_Class81.Rxjh_X = -82f;
		x_Toa_Do_Class81.Rxjh_Y = -23f;
		x_Toa_Do_Class81.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class81);
		X_Toa_Do_Class x_Toa_Do_Class82 = new();
		x_Toa_Do_Class82.Rxjh_Map = 40101;
		x_Toa_Do_Class82.Rxjh_X = -74f;
		x_Toa_Do_Class82.Rxjh_Y = -72f;
		x_Toa_Do_Class82.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class82);
		X_Toa_Do_Class x_Toa_Do_Class83 = new();
		x_Toa_Do_Class83.Rxjh_Map = 40101;
		x_Toa_Do_Class83.Rxjh_X = -74f;
		x_Toa_Do_Class83.Rxjh_Y = -87f;
		x_Toa_Do_Class83.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class83);
		X_Toa_Do_Class x_Toa_Do_Class84 = new();
		x_Toa_Do_Class84.Rxjh_Map = 40101;
		x_Toa_Do_Class84.Rxjh_X = -74f;
		x_Toa_Do_Class84.Rxjh_Y = -69f;
		x_Toa_Do_Class84.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class84);
		X_Toa_Do_Class x_Toa_Do_Class85 = new();
		x_Toa_Do_Class85.Rxjh_Map = 40101;
		x_Toa_Do_Class85.Rxjh_X = -63f;
		x_Toa_Do_Class85.Rxjh_Y = -119f;
		x_Toa_Do_Class85.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class85);
		X_Toa_Do_Class x_Toa_Do_Class86 = new();
		x_Toa_Do_Class86.Rxjh_Map = 40101;
		x_Toa_Do_Class86.Rxjh_X = -80f;
		x_Toa_Do_Class86.Rxjh_Y = -121f;
		x_Toa_Do_Class86.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class86);
		X_Toa_Do_Class x_Toa_Do_Class87 = new();
		x_Toa_Do_Class87.Rxjh_Map = 40101;
		x_Toa_Do_Class87.Rxjh_X = -74f;
		x_Toa_Do_Class87.Rxjh_Y = -126f;
		x_Toa_Do_Class87.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class87);
		X_Toa_Do_Class x_Toa_Do_Class88 = new();
		x_Toa_Do_Class35.Rxjh_Map = 40101;
		x_Toa_Do_Class35.Rxjh_X = -63f;
		x_Toa_Do_Class35.Rxjh_Y = -134f;
		x_Toa_Do_Class35.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class35);
		X_Toa_Do_Class x_Toa_Do_Class89 = new();
		x_Toa_Do_Class89.Rxjh_Map = 40101;
		x_Toa_Do_Class89.Rxjh_X = -63f;
		x_Toa_Do_Class89.Rxjh_Y = -134f;
		x_Toa_Do_Class89.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class89);
		X_Toa_Do_Class x_Toa_Do_Class90 = new();
		x_Toa_Do_Class90.Rxjh_Map = 40101;
		x_Toa_Do_Class90.Rxjh_X = -78f;
		x_Toa_Do_Class90.Rxjh_Y = -133f;
		x_Toa_Do_Class90.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class90);
		X_Toa_Do_Class x_Toa_Do_Class91 = new();
		x_Toa_Do_Class91.Rxjh_Map = 40101;
		x_Toa_Do_Class91.Rxjh_X = -111f;
		x_Toa_Do_Class91.Rxjh_Y = -69f;
		x_Toa_Do_Class91.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class91);
		X_Toa_Do_Class x_Toa_Do_Class92 = new();
		x_Toa_Do_Class92.Rxjh_Map = 40101;
		x_Toa_Do_Class92.Rxjh_X = -88f;
		x_Toa_Do_Class92.Rxjh_Y = -73f;
		x_Toa_Do_Class92.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class92);
		X_Toa_Do_Class x_Toa_Do_Class93 = new();
		x_Toa_Do_Class93.Rxjh_Map = 40101;
		x_Toa_Do_Class93.Rxjh_X = -89f;
		x_Toa_Do_Class93.Rxjh_Y = -87f;
		x_Toa_Do_Class93.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class93);
		X_Toa_Do_Class x_Toa_Do_Class94 = new();
		x_Toa_Do_Class94.Rxjh_Map = 40101;
		x_Toa_Do_Class94.Rxjh_X = -99f;
		x_Toa_Do_Class94.Rxjh_Y = -94f;
		x_Toa_Do_Class94.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class94);
		X_Toa_Do_Class x_Toa_Do_Class95 = new();
		x_Toa_Do_Class95.Rxjh_Map = 40101;
		x_Toa_Do_Class95.Rxjh_X = -112f;
		x_Toa_Do_Class95.Rxjh_Y = -100f;
		x_Toa_Do_Class95.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class95);
		X_Toa_Do_Class x_Toa_Do_Class96 = new();
		x_Toa_Do_Class96.Rxjh_Map = 40101;
		x_Toa_Do_Class96.Rxjh_X = -110f;
		x_Toa_Do_Class96.Rxjh_Y = -115f;
		x_Toa_Do_Class96.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class96);
		X_Toa_Do_Class x_Toa_Do_Class97 = new();
		x_Toa_Do_Class97.Rxjh_Map = 40101;
		x_Toa_Do_Class97.Rxjh_X = -123f;
		x_Toa_Do_Class97.Rxjh_Y = -132f;
		x_Toa_Do_Class97.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class97);
		X_Toa_Do_Class x_Toa_Do_Class98 = new();
		x_Toa_Do_Class98.Rxjh_Map = 40101;
		x_Toa_Do_Class98.Rxjh_X = -121f;
		x_Toa_Do_Class98.Rxjh_Y = -150f;
		x_Toa_Do_Class98.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class98);
		X_Toa_Do_Class x_Toa_Do_Class99 = new();
		x_Toa_Do_Class99.Rxjh_Map = 40101;
		x_Toa_Do_Class99.Rxjh_X = -78f;
		x_Toa_Do_Class99.Rxjh_Y = -146f;
		x_Toa_Do_Class99.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class99);
		X_Toa_Do_Class x_Toa_Do_Class100 = new();
		x_Toa_Do_Class100.Rxjh_Map = 40101;
		x_Toa_Do_Class100.Rxjh_X = -63f;
		x_Toa_Do_Class100.Rxjh_Y = -149f;
		x_Toa_Do_Class100.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class100);
		X_Toa_Do_Class x_Toa_Do_Class101 = new();
		x_Toa_Do_Class101.Rxjh_Map = 40101;
		x_Toa_Do_Class101.Rxjh_X = -64f;
		x_Toa_Do_Class101.Rxjh_Y = -165f;
		x_Toa_Do_Class101.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class101);
		X_Toa_Do_Class x_Toa_Do_Class102 = new();
		x_Toa_Do_Class102.Rxjh_Map = 40101;
		x_Toa_Do_Class102.Rxjh_X = -79f;
		x_Toa_Do_Class102.Rxjh_Y = -171f;
		x_Toa_Do_Class102.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class102);
		X_Toa_Do_Class x_Toa_Do_Class103 = new();
		x_Toa_Do_Class103.Rxjh_Map = 40101;
		x_Toa_Do_Class103.Rxjh_X = -65f;
		x_Toa_Do_Class103.Rxjh_Y = -182f;
		x_Toa_Do_Class103.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class103);
		X_Toa_Do_Class x_Toa_Do_Class104 = new();
		x_Toa_Do_Class104.Rxjh_Map = 40101;
		x_Toa_Do_Class104.Rxjh_X = -188f;
		x_Toa_Do_Class104.Rxjh_Y = -80f;
		x_Toa_Do_Class104.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class104);
		X_Toa_Do_Class x_Toa_Do_Class105 = new();
		x_Toa_Do_Class105.Rxjh_Map = 40101;
		x_Toa_Do_Class105.Rxjh_X = -146f;
		x_Toa_Do_Class105.Rxjh_Y = -78f;
		x_Toa_Do_Class105.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class105);
		X_Toa_Do_Class x_Toa_Do_Class106 = new();
		x_Toa_Do_Class106.Rxjh_Map = 40101;
		x_Toa_Do_Class106.Rxjh_X = -140f;
		x_Toa_Do_Class106.Rxjh_Y = -96f;
		x_Toa_Do_Class106.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class106);
		X_Toa_Do_Class x_Toa_Do_Class107 = new();
		x_Toa_Do_Class107.Rxjh_Map = 40101;
		x_Toa_Do_Class107.Rxjh_X = -152f;
		x_Toa_Do_Class107.Rxjh_Y = -91f;
		x_Toa_Do_Class107.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class107);
		X_Toa_Do_Class x_Toa_Do_Class108 = new();
		x_Toa_Do_Class108.Rxjh_Map = 40101;
		x_Toa_Do_Class108.Rxjh_X = -155f;
		x_Toa_Do_Class108.Rxjh_Y = -105f;
		x_Toa_Do_Class108.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class108);
		X_Toa_Do_Class x_Toa_Do_Class109 = new();
		x_Toa_Do_Class109.Rxjh_Map = 40101;
		x_Toa_Do_Class109.Rxjh_X = -169f;
		x_Toa_Do_Class109.Rxjh_Y = -116f;
		x_Toa_Do_Class109.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class109);
		X_Toa_Do_Class x_Toa_Do_Class110 = new();
		x_Toa_Do_Class110.Rxjh_Map = 40101;
		x_Toa_Do_Class110.Rxjh_X = -157f;
		x_Toa_Do_Class110.Rxjh_Y = -124f;
		x_Toa_Do_Class110.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class110);
		X_Toa_Do_Class x_Toa_Do_Class111 = new();
		x_Toa_Do_Class111.Rxjh_Map = 40101;
		x_Toa_Do_Class111.Rxjh_X = -167f;
		x_Toa_Do_Class111.Rxjh_Y = -138f;
		x_Toa_Do_Class111.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class111);
		X_Toa_Do_Class x_Toa_Do_Class112 = new();
		x_Toa_Do_Class112.Rxjh_Map = 40101;
		x_Toa_Do_Class112.Rxjh_X = -152f;
		x_Toa_Do_Class112.Rxjh_Y = -140f;
		x_Toa_Do_Class112.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class112);
		X_Toa_Do_Class x_Toa_Do_Class113 = new();
		x_Toa_Do_Class113.Rxjh_Map = 40101;
		x_Toa_Do_Class113.Rxjh_X = -94f;
		x_Toa_Do_Class113.Rxjh_Y = -49f;
		x_Toa_Do_Class113.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class113);
		X_Toa_Do_Class x_Toa_Do_Class114 = new();
		x_Toa_Do_Class114.Rxjh_Map = 40101;
		x_Toa_Do_Class114.Rxjh_X = 92f;
		x_Toa_Do_Class114.Rxjh_Y = -44f;
		x_Toa_Do_Class114.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class114);
		X_Toa_Do_Class x_Toa_Do_Class115 = new();
		x_Toa_Do_Class115.Rxjh_Map = 40101;
		x_Toa_Do_Class115.Rxjh_X = 94f;
		x_Toa_Do_Class115.Rxjh_Y = -59f;
		x_Toa_Do_Class115.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class115);
		X_Toa_Do_Class x_Toa_Do_Class116 = new();
		x_Toa_Do_Class116.Rxjh_Map = 40101;
		x_Toa_Do_Class116.Rxjh_X = 102f;
		x_Toa_Do_Class116.Rxjh_Y = -44f;
		x_Toa_Do_Class116.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class116);
		X_Toa_Do_Class x_Toa_Do_Class117 = new();
		x_Toa_Do_Class117.Rxjh_Map = 40101;
		x_Toa_Do_Class117.Rxjh_X = 144f;
		x_Toa_Do_Class117.Rxjh_Y = -44f;
		x_Toa_Do_Class117.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class117);
		X_Toa_Do_Class x_Toa_Do_Class118 = new();
		x_Toa_Do_Class118.Rxjh_Map = 40101;
		x_Toa_Do_Class118.Rxjh_X = 147f;
		x_Toa_Do_Class118.Rxjh_Y = -59f;
		x_Toa_Do_Class118.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class118);
		X_Toa_Do_Class x_Toa_Do_Class119 = new();
		x_Toa_Do_Class119.Rxjh_Map = 40101;
		x_Toa_Do_Class119.Rxjh_X = -220f;
		x_Toa_Do_Class119.Rxjh_Y = -145f;
		x_Toa_Do_Class119.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class119);
		X_Toa_Do_Class x_Toa_Do_Class120 = new();
		x_Toa_Do_Class120.Rxjh_Map = 40101;
		x_Toa_Do_Class120.Rxjh_X = 115f;
		x_Toa_Do_Class120.Rxjh_Y = -44f;
		x_Toa_Do_Class120.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class120);
		X_Toa_Do_Class x_Toa_Do_Class121 = new();
		x_Toa_Do_Class121.Rxjh_Map = 40101;
		x_Toa_Do_Class121.Rxjh_X = -131f;
		x_Toa_Do_Class121.Rxjh_Y = 43f;
		x_Toa_Do_Class121.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class121);
		X_Toa_Do_Class x_Toa_Do_Class122 = new();
		x_Toa_Do_Class122.Rxjh_Map = 40101;
		x_Toa_Do_Class122.Rxjh_X = 131f;
		x_Toa_Do_Class122.Rxjh_Y = -55f;
		x_Toa_Do_Class122.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class122);
		X_Toa_Do_Class x_Toa_Do_Class123 = new();
		x_Toa_Do_Class123.Rxjh_Map = 40101;
		x_Toa_Do_Class123.Rxjh_X = 122f;
		x_Toa_Do_Class123.Rxjh_Y = -57f;
		x_Toa_Do_Class123.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class123);
		X_Toa_Do_Class x_Toa_Do_Class124 = new();
		x_Toa_Do_Class124.Rxjh_Map = 40101;
		x_Toa_Do_Class124.Rxjh_X = 111f;
		x_Toa_Do_Class124.Rxjh_Y = -56f;
		x_Toa_Do_Class124.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class124);
		X_Toa_Do_Class x_Toa_Do_Class125 = new();
		x_Toa_Do_Class125.Rxjh_Map = 40101;
		x_Toa_Do_Class125.Rxjh_X = 120f;
		x_Toa_Do_Class125.Rxjh_Y = -67f;
		x_Toa_Do_Class125.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class125);
		X_Toa_Do_Class x_Toa_Do_Class126 = new();
		x_Toa_Do_Class126.Rxjh_Map = 40101;
		x_Toa_Do_Class126.Rxjh_X = 121f;
		x_Toa_Do_Class126.Rxjh_Y = -81f;
		x_Toa_Do_Class126.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class126);
		X_Toa_Do_Class x_Toa_Do_Class127 = new();
		x_Toa_Do_Class127.Rxjh_Map = 40101;
		x_Toa_Do_Class127.Rxjh_X = 120f;
		x_Toa_Do_Class127.Rxjh_Y = -93f;
		x_Toa_Do_Class127.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class127);
		X_Toa_Do_Class x_Toa_Do_Class128 = new();
		x_Toa_Do_Class128.Rxjh_Map = 40101;
		x_Toa_Do_Class128.Rxjh_X = 119f;
		x_Toa_Do_Class128.Rxjh_Y = -105f;
		x_Toa_Do_Class128.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class128);
		X_Toa_Do_Class x_Toa_Do_Class129 = new();
		x_Toa_Do_Class129.Rxjh_Map = 40101;
		x_Toa_Do_Class129.Rxjh_X = 118f;
		x_Toa_Do_Class129.Rxjh_Y = -117f;
		x_Toa_Do_Class129.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class129);
		X_Toa_Do_Class x_Toa_Do_Class130 = new();
		x_Toa_Do_Class130.Rxjh_Map = 40101;
		x_Toa_Do_Class130.Rxjh_X = 134f;
		x_Toa_Do_Class130.Rxjh_Y = -93f;
		x_Toa_Do_Class130.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class130);
		X_Toa_Do_Class x_Toa_Do_Class131 = new();
		x_Toa_Do_Class131.Rxjh_Map = 40101;
		x_Toa_Do_Class131.Rxjh_X = 148f;
		x_Toa_Do_Class131.Rxjh_Y = -59f;
		x_Toa_Do_Class131.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class131);
		X_Toa_Do_Class x_Toa_Do_Class132 = new();
		x_Toa_Do_Class132.Rxjh_Map = 40101;
		x_Toa_Do_Class132.Rxjh_X = 157f;
		x_Toa_Do_Class132.Rxjh_Y = -70f;
		x_Toa_Do_Class132.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class132);
		X_Toa_Do_Class x_Toa_Do_Class133 = new();
		x_Toa_Do_Class133.Rxjh_Map = 40101;
		x_Toa_Do_Class133.Rxjh_X = 166f;
		x_Toa_Do_Class133.Rxjh_Y = -71f;
		x_Toa_Do_Class133.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class133);
		X_Toa_Do_Class x_Toa_Do_Class134 = new();
		x_Toa_Do_Class134.Rxjh_Map = 40101;
		x_Toa_Do_Class134.Rxjh_X = 153f;
		x_Toa_Do_Class134.Rxjh_Y = -122f;
		x_Toa_Do_Class134.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class134);
		X_Toa_Do_Class x_Toa_Do_Class135 = new();
		x_Toa_Do_Class135.Rxjh_Map = 40101;
		x_Toa_Do_Class135.Rxjh_X = 139f;
		x_Toa_Do_Class135.Rxjh_Y = -123f;
		x_Toa_Do_Class135.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class135);
		X_Toa_Do_Class x_Toa_Do_Class136 = new();
		x_Toa_Do_Class136.Rxjh_Map = 40101;
		x_Toa_Do_Class136.Rxjh_X = 135f;
		x_Toa_Do_Class136.Rxjh_Y = -134f;
		x_Toa_Do_Class136.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class136);
		X_Toa_Do_Class x_Toa_Do_Class137 = new();
		x_Toa_Do_Class137.Rxjh_Map = 40101;
		x_Toa_Do_Class137.Rxjh_X = 136f;
		x_Toa_Do_Class137.Rxjh_Y = -143f;
		x_Toa_Do_Class137.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class137);
		X_Toa_Do_Class x_Toa_Do_Class138 = new();
		x_Toa_Do_Class138.Rxjh_Map = 40101;
		x_Toa_Do_Class138.Rxjh_X = 138f;
		x_Toa_Do_Class138.Rxjh_Y = -151f;
		x_Toa_Do_Class138.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class138);
		X_Toa_Do_Class x_Toa_Do_Class139 = new();
		x_Toa_Do_Class139.Rxjh_Map = 40101;
		x_Toa_Do_Class139.Rxjh_X = 80f;
		x_Toa_Do_Class139.Rxjh_Y = -120f;
		x_Toa_Do_Class139.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class139);
		X_Toa_Do_Class x_Toa_Do_Class140 = new();
		x_Toa_Do_Class140.Rxjh_Map = 40101;
		x_Toa_Do_Class140.Rxjh_X = 69f;
		x_Toa_Do_Class140.Rxjh_Y = -119f;
		x_Toa_Do_Class140.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class140);
		X_Toa_Do_Class x_Toa_Do_Class141 = new();
		x_Toa_Do_Class141.Rxjh_Map = 40101;
		x_Toa_Do_Class141.Rxjh_X = 69f;
		x_Toa_Do_Class141.Rxjh_Y = -127f;
		x_Toa_Do_Class141.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class141);
		X_Toa_Do_Class x_Toa_Do_Class142 = new();
		x_Toa_Do_Class142.Rxjh_Map = 40101;
		x_Toa_Do_Class142.Rxjh_X = 69f;
		x_Toa_Do_Class142.Rxjh_Y = -136f;
		x_Toa_Do_Class142.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class142);
		X_Toa_Do_Class x_Toa_Do_Class143 = new();
		x_Toa_Do_Class143.Rxjh_Map = 40101;
		x_Toa_Do_Class143.Rxjh_X = 69f;
		x_Toa_Do_Class143.Rxjh_Y = -145f;
		x_Toa_Do_Class143.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class143);
		X_Toa_Do_Class x_Toa_Do_Class144 = new();
		x_Toa_Do_Class144.Rxjh_Map = 40101;
		x_Toa_Do_Class144.Rxjh_X = 69f;
		x_Toa_Do_Class144.Rxjh_Y = -153f;
		x_Toa_Do_Class144.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class144);
		X_Toa_Do_Class x_Toa_Do_Class145 = new();
		x_Toa_Do_Class145.Rxjh_Map = 40101;
		x_Toa_Do_Class145.Rxjh_X = 69f;
		x_Toa_Do_Class145.Rxjh_Y = -164f;
		x_Toa_Do_Class145.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class145);
		X_Toa_Do_Class x_Toa_Do_Class146 = new();
		x_Toa_Do_Class146.Rxjh_Map = 40101;
		x_Toa_Do_Class146.Rxjh_X = 69f;
		x_Toa_Do_Class146.Rxjh_Y = -174f;
		x_Toa_Do_Class146.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class146);
		X_Toa_Do_Class x_Toa_Do_Class147 = new();
		x_Toa_Do_Class147.Rxjh_Map = 40101;
		x_Toa_Do_Class147.Rxjh_X = 69f;
		x_Toa_Do_Class147.Rxjh_Y = -184f;
		x_Toa_Do_Class147.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class147);
		X_Toa_Do_Class x_Toa_Do_Class148 = new();
		x_Toa_Do_Class148.Rxjh_Map = 40101;
		x_Toa_Do_Class148.Rxjh_X = -220f;
		x_Toa_Do_Class148.Rxjh_Y = -145f;
		x_Toa_Do_Class35.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class148);
		X_Toa_Do_Class x_Toa_Do_Class149 = new();
		x_Toa_Do_Class149.Rxjh_Map = 40101;
		x_Toa_Do_Class149.Rxjh_X = 80f;
		x_Toa_Do_Class149.Rxjh_Y = -127f;
		x_Toa_Do_Class149.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class149);
		X_Toa_Do_Class x_Toa_Do_Class150 = new();
		x_Toa_Do_Class150.Rxjh_Map = 40101;
		x_Toa_Do_Class150.Rxjh_X = 80f;
		x_Toa_Do_Class150.Rxjh_Y = -138f;
		x_Toa_Do_Class150.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class150);
		X_Toa_Do_Class x_Toa_Do_Class151 = new();
		x_Toa_Do_Class151.Rxjh_Map = 40101;
		x_Toa_Do_Class151.Rxjh_X = 80f;
		x_Toa_Do_Class151.Rxjh_Y = -154f;
		x_Toa_Do_Class151.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class151);
		X_Toa_Do_Class x_Toa_Do_Class152 = new();
		x_Toa_Do_Class152.Rxjh_Map = 40101;
		x_Toa_Do_Class152.Rxjh_X = 80f;
		x_Toa_Do_Class152.Rxjh_Y = -162f;
		x_Toa_Do_Class152.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class152);
		X_Toa_Do_Class x_Toa_Do_Class153 = new();
		x_Toa_Do_Class153.Rxjh_Map = 40101;
		x_Toa_Do_Class153.Rxjh_X = 80f;
		x_Toa_Do_Class153.Rxjh_Y = -171f;
		x_Toa_Do_Class153.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class153);
		X_Toa_Do_Class x_Toa_Do_Class154 = new();
		x_Toa_Do_Class154.Rxjh_Map = 40101;
		x_Toa_Do_Class154.Rxjh_X = 80f;
		x_Toa_Do_Class154.Rxjh_Y = -179f;
		x_Toa_Do_Class154.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class154);
		X_Toa_Do_Class x_Toa_Do_Class155 = new();
		x_Toa_Do_Class155.Rxjh_Map = 40101;
		x_Toa_Do_Class155.Rxjh_X = 80f;
		x_Toa_Do_Class155.Rxjh_Y = -186f;
		x_Toa_Do_Class155.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class155);
		X_Toa_Do_Class x_Toa_Do_Class156 = new();
		x_Toa_Do_Class156.Rxjh_Map = 40101;
		x_Toa_Do_Class156.Rxjh_X = 186f;
		x_Toa_Do_Class156.Rxjh_Y = -57f;
		x_Toa_Do_Class156.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class156);
		X_Toa_Do_Class x_Toa_Do_Class157 = new();
		x_Toa_Do_Class157.Rxjh_Map = 40101;
		x_Toa_Do_Class157.Rxjh_X = 186f;
		x_Toa_Do_Class157.Rxjh_Y = -49f;
		x_Toa_Do_Class157.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class157);
		X_Toa_Do_Class x_Toa_Do_Class158 = new();
		x_Toa_Do_Class158.Rxjh_Map = 40101;
		x_Toa_Do_Class158.Rxjh_X = 186f;
		x_Toa_Do_Class158.Rxjh_Y = -40f;
		x_Toa_Do_Class158.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class158);
		X_Toa_Do_Class x_Toa_Do_Class159 = new();
		x_Toa_Do_Class159.Rxjh_Map = 40101;
		x_Toa_Do_Class159.Rxjh_X = 186f;
		x_Toa_Do_Class159.Rxjh_Y = -29f;
		x_Toa_Do_Class159.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class159);
		X_Toa_Do_Class x_Toa_Do_Class160 = new();
		x_Toa_Do_Class160.Rxjh_Map = 40101;
		x_Toa_Do_Class160.Rxjh_X = 186f;
		x_Toa_Do_Class160.Rxjh_Y = -18f;
		x_Toa_Do_Class160.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class160);
		X_Toa_Do_Class x_Toa_Do_Class161 = new();
		x_Toa_Do_Class161.Rxjh_Map = 40101;
		x_Toa_Do_Class161.Rxjh_X = 199f;
		x_Toa_Do_Class161.Rxjh_Y = -18f;
		x_Toa_Do_Class161.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class161);
		X_Toa_Do_Class x_Toa_Do_Class162 = new();
		x_Toa_Do_Class162.Rxjh_Map = 40101;
		x_Toa_Do_Class162.Rxjh_X = 199f;
		x_Toa_Do_Class162.Rxjh_Y = -28f;
		x_Toa_Do_Class162.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class162);
		X_Toa_Do_Class x_Toa_Do_Class163 = new();
		x_Toa_Do_Class163.Rxjh_Map = 40101;
		x_Toa_Do_Class163.Rxjh_X = 199f;
		x_Toa_Do_Class163.Rxjh_Y = -55f;
		x_Toa_Do_Class163.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class163);
		X_Toa_Do_Class x_Toa_Do_Class164 = new();
		x_Toa_Do_Class164.Rxjh_Map = 40101;
		x_Toa_Do_Class164.Rxjh_X = 241f;
		x_Toa_Do_Class164.Rxjh_Y = -38f;
		x_Toa_Do_Class164.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class164);
		X_Toa_Do_Class x_Toa_Do_Class165 = new();
		x_Toa_Do_Class165.Rxjh_Map = 40101;
		x_Toa_Do_Class165.Rxjh_X = 241f;
		x_Toa_Do_Class165.Rxjh_Y = -50f;
		x_Toa_Do_Class165.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class165);
		X_Toa_Do_Class x_Toa_Do_Class166 = new();
		x_Toa_Do_Class166.Rxjh_Map = 40101;
		x_Toa_Do_Class166.Rxjh_X = 252f;
		x_Toa_Do_Class166.Rxjh_Y = -52f;
		x_Toa_Do_Class166.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class166);
		X_Toa_Do_Class x_Toa_Do_Class167 = new();
		x_Toa_Do_Class167.Rxjh_Map = 40101;
		x_Toa_Do_Class167.Rxjh_X = 262f;
		x_Toa_Do_Class167.Rxjh_Y = -55f;
		x_Toa_Do_Class167.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class167);
		X_Toa_Do_Class x_Toa_Do_Class168 = new();
		x_Toa_Do_Class168.Rxjh_Map = 40101;
		x_Toa_Do_Class168.Rxjh_X = 258f;
		x_Toa_Do_Class168.Rxjh_Y = -64f;
		x_Toa_Do_Class168.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class168);
		X_Toa_Do_Class x_Toa_Do_Class169 = new();
		x_Toa_Do_Class169.Rxjh_Map = 40101;
		x_Toa_Do_Class169.Rxjh_X = 269f;
		x_Toa_Do_Class169.Rxjh_Y = -62f;
		x_Toa_Do_Class169.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class169);
		X_Toa_Do_Class x_Toa_Do_Class170 = new();
		x_Toa_Do_Class170.Rxjh_Map = 40101;
		x_Toa_Do_Class170.Rxjh_X = 278f;
		x_Toa_Do_Class170.Rxjh_Y = -44f;
		x_Toa_Do_Class170.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class170);
		X_Toa_Do_Class x_Toa_Do_Class171 = new();
		x_Toa_Do_Class171.Rxjh_Map = 40101;
		x_Toa_Do_Class171.Rxjh_X = 279f;
		x_Toa_Do_Class171.Rxjh_Y = -57f;
		x_Toa_Do_Class171.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class171);
		X_Toa_Do_Class x_Toa_Do_Class172 = new();
		x_Toa_Do_Class172.Rxjh_Map = 40101;
		x_Toa_Do_Class172.Rxjh_X = 280f;
		x_Toa_Do_Class172.Rxjh_Y = -63f;
		x_Toa_Do_Class172.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class172);
		X_Toa_Do_Class x_Toa_Do_Class173 = new();
		x_Toa_Do_Class173.Rxjh_Map = 40101;
		x_Toa_Do_Class173.Rxjh_X = 289f;
		x_Toa_Do_Class173.Rxjh_Y = -64f;
		x_Toa_Do_Class173.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class173);
		X_Toa_Do_Class x_Toa_Do_Class174 = new();
		x_Toa_Do_Class174.Rxjh_Map = 40101;
		x_Toa_Do_Class174.Rxjh_X = 301f;
		x_Toa_Do_Class174.Rxjh_Y = -64f;
		x_Toa_Do_Class174.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class174);
		X_Toa_Do_Class x_Toa_Do_Class175 = new();
		x_Toa_Do_Class175.Rxjh_Map = 40101;
		x_Toa_Do_Class175.Rxjh_X = 283f;
		x_Toa_Do_Class175.Rxjh_Y = -103f;
		x_Toa_Do_Class175.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class175);
		X_Toa_Do_Class x_Toa_Do_Class176 = new();
		x_Toa_Do_Class176.Rxjh_Map = 40101;
		x_Toa_Do_Class176.Rxjh_X = 293f;
		x_Toa_Do_Class176.Rxjh_Y = -111f;
		x_Toa_Do_Class176.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class176);
		X_Toa_Do_Class x_Toa_Do_Class177 = new();
		x_Toa_Do_Class177.Rxjh_Map = 40101;
		x_Toa_Do_Class177.Rxjh_X = 281f;
		x_Toa_Do_Class177.Rxjh_Y = -17f;
		x_Toa_Do_Class177.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class177);
		X_Toa_Do_Class x_Toa_Do_Class178 = new();
		x_Toa_Do_Class178.Rxjh_Map = 40101;
		x_Toa_Do_Class178.Rxjh_X = 291f;
		x_Toa_Do_Class178.Rxjh_Y = -122f;
		x_Toa_Do_Class178.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class178);
		X_Toa_Do_Class x_Toa_Do_Class179 = new();
		x_Toa_Do_Class179.Rxjh_Map = 40101;
		x_Toa_Do_Class179.Rxjh_X = 294f;
		x_Toa_Do_Class179.Rxjh_Y = -136f;
		x_Toa_Do_Class179.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class179);
		X_Toa_Do_Class x_Toa_Do_Class180 = new();
		x_Toa_Do_Class180.Rxjh_Map = 40101;
		x_Toa_Do_Class180.Rxjh_X = 292f;
		x_Toa_Do_Class180.Rxjh_Y = -146f;
		x_Toa_Do_Class180.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class180);
		X_Toa_Do_Class x_Toa_Do_Class181 = new();
		x_Toa_Do_Class181.Rxjh_Map = 40101;
		x_Toa_Do_Class181.Rxjh_X = 291f;
		x_Toa_Do_Class181.Rxjh_Y = -155f;
		x_Toa_Do_Class181.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class181);
		X_Toa_Do_Class x_Toa_Do_Class182 = new();
		x_Toa_Do_Class182.Rxjh_Map = 40101;
		x_Toa_Do_Class182.Rxjh_X = 245f;
		x_Toa_Do_Class182.Rxjh_Y = -101f;
		x_Toa_Do_Class182.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class182);
		X_Toa_Do_Class x_Toa_Do_Class183 = new();
		x_Toa_Do_Class183.Rxjh_Map = 40101;
		x_Toa_Do_Class183.Rxjh_X = 248f;
		x_Toa_Do_Class183.Rxjh_Y = -115f;
		x_Toa_Do_Class183.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class183);
		X_Toa_Do_Class x_Toa_Do_Class184 = new();
		x_Toa_Do_Class184.Rxjh_Map = 40101;
		x_Toa_Do_Class184.Rxjh_X = 243f;
		x_Toa_Do_Class184.Rxjh_Y = -128f;
		x_Toa_Do_Class184.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class184);
		X_Toa_Do_Class x_Toa_Do_Class185 = new();
		x_Toa_Do_Class185.Rxjh_Map = 40101;
		x_Toa_Do_Class185.Rxjh_X = 223f;
		x_Toa_Do_Class185.Rxjh_Y = -105f;
		x_Toa_Do_Class185.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class185);
		X_Toa_Do_Class x_Toa_Do_Class186 = new();
		x_Toa_Do_Class186.Rxjh_Map = 40101;
		x_Toa_Do_Class186.Rxjh_X = 224f;
		x_Toa_Do_Class186.Rxjh_Y = -122f;
		x_Toa_Do_Class186.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class186);
		X_Toa_Do_Class x_Toa_Do_Class187 = new();
		x_Toa_Do_Class187.Rxjh_Map = 40101;
		x_Toa_Do_Class187.Rxjh_X = 223f;
		x_Toa_Do_Class187.Rxjh_Y = -138f;
		x_Toa_Do_Class187.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class187);
		X_Toa_Do_Class x_Toa_Do_Class188 = new();
		x_Toa_Do_Class188.Rxjh_Map = 40101;
		x_Toa_Do_Class188.Rxjh_X = 223f;
		x_Toa_Do_Class188.Rxjh_Y = -149f;
		x_Toa_Do_Class188.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class188);
		X_Toa_Do_Class x_Toa_Do_Class189 = new();
		x_Toa_Do_Class189.Rxjh_Map = 40101;
		x_Toa_Do_Class189.Rxjh_X = 206f;
		x_Toa_Do_Class189.Rxjh_Y = -104f;
		x_Toa_Do_Class189.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class189);
		X_Toa_Do_Class x_Toa_Do_Class190 = new();
		x_Toa_Do_Class190.Rxjh_Map = 40101;
		x_Toa_Do_Class190.Rxjh_X = 206f;
		x_Toa_Do_Class190.Rxjh_Y = -97f;
		x_Toa_Do_Class190.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class190);
		X_Toa_Do_Class x_Toa_Do_Class191 = new();
		x_Toa_Do_Class191.Rxjh_Map = 40101;
		x_Toa_Do_Class191.Rxjh_X = 206f;
		x_Toa_Do_Class191.Rxjh_Y = -91f;
		x_Toa_Do_Class191.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class191);
		X_Toa_Do_Class x_Toa_Do_Class192 = new();
		x_Toa_Do_Class192.Rxjh_Map = 40101;
		x_Toa_Do_Class192.Rxjh_X = 287f;
		x_Toa_Do_Class192.Rxjh_Y = 39f;
		x_Toa_Do_Class192.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class192);
		X_Toa_Do_Class x_Toa_Do_Class193 = new();
		x_Toa_Do_Class193.Rxjh_Map = 40101;
		x_Toa_Do_Class193.Rxjh_X = 287f;
		x_Toa_Do_Class193.Rxjh_Y = 51f;
		x_Toa_Do_Class193.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class193);
		X_Toa_Do_Class x_Toa_Do_Class194 = new();
		x_Toa_Do_Class194.Rxjh_Map = 40101;
		x_Toa_Do_Class194.Rxjh_X = 276f;
		x_Toa_Do_Class194.Rxjh_Y = 52f;
		x_Toa_Do_Class194.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class194);
		X_Toa_Do_Class x_Toa_Do_Class195 = new();
		x_Toa_Do_Class195.Rxjh_Map = 40101;
		x_Toa_Do_Class195.Rxjh_X = 279f;
		x_Toa_Do_Class195.Rxjh_Y = 60f;
		x_Toa_Do_Class195.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class195);
		X_Toa_Do_Class x_Toa_Do_Class196 = new();
		x_Toa_Do_Class196.Rxjh_Map = 40101;
		x_Toa_Do_Class196.Rxjh_X = 274f;
		x_Toa_Do_Class196.Rxjh_Y = 67f;
		x_Toa_Do_Class196.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class196);
		X_Toa_Do_Class x_Toa_Do_Class197 = new();
		x_Toa_Do_Class197.Rxjh_Map = 40101;
		x_Toa_Do_Class197.Rxjh_X = 259f;
		x_Toa_Do_Class197.Rxjh_Y = 105f;
		x_Toa_Do_Class197.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class197);
		X_Toa_Do_Class x_Toa_Do_Class198 = new();
		x_Toa_Do_Class198.Rxjh_Map = 40101;
		x_Toa_Do_Class198.Rxjh_X = 258f;
		x_Toa_Do_Class198.Rxjh_Y = 116f;
		x_Toa_Do_Class198.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class198);
		X_Toa_Do_Class x_Toa_Do_Class199 = new();
		x_Toa_Do_Class199.Rxjh_Map = 40101;
		x_Toa_Do_Class199.Rxjh_X = 249f;
		x_Toa_Do_Class199.Rxjh_Y = 120f;
		x_Toa_Do_Class199.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class199);
		X_Toa_Do_Class x_Toa_Do_Class200 = new();
		x_Toa_Do_Class200.Rxjh_Map = 40101;
		x_Toa_Do_Class200.Rxjh_X = 246f;
		x_Toa_Do_Class200.Rxjh_Y = 131f;
		x_Toa_Do_Class200.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class200);
		X_Toa_Do_Class x_Toa_Do_Class201 = new();
		x_Toa_Do_Class201.Rxjh_Map = 40101;
		x_Toa_Do_Class201.Rxjh_X = 246f;
		x_Toa_Do_Class201.Rxjh_Y = 140f;
		x_Toa_Do_Class201.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class201);
		X_Toa_Do_Class x_Toa_Do_Class202 = new();
		x_Toa_Do_Class202.Rxjh_Map = 40101;
		x_Toa_Do_Class202.Rxjh_X = 261f;
		x_Toa_Do_Class202.Rxjh_Y = 145f;
		x_Toa_Do_Class202.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class202);
		X_Toa_Do_Class x_Toa_Do_Class203 = new();
		x_Toa_Do_Class203.Rxjh_Map = 40101;
		x_Toa_Do_Class203.Rxjh_X = 252f;
		x_Toa_Do_Class203.Rxjh_Y = 150f;
		x_Toa_Do_Class203.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class203);
		X_Toa_Do_Class x_Toa_Do_Class204 = new();
		x_Toa_Do_Class204.Rxjh_Map = 40101;
		x_Toa_Do_Class204.Rxjh_X = 261f;
		x_Toa_Do_Class204.Rxjh_Y = 161f;
		x_Toa_Do_Class204.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class204);
		X_Toa_Do_Class x_Toa_Do_Class205 = new();
		x_Toa_Do_Class205.Rxjh_Map = 40101;
		x_Toa_Do_Class205.Rxjh_X = 250f;
		x_Toa_Do_Class205.Rxjh_Y = 164f;
		x_Toa_Do_Class205.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class205);
		X_Toa_Do_Class x_Toa_Do_Class206 = new();
		x_Toa_Do_Class206.Rxjh_Map = 40101;
		x_Toa_Do_Class206.Rxjh_X = 260f;
		x_Toa_Do_Class206.Rxjh_Y = 173f;
		x_Toa_Do_Class206.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class206);
		X_Toa_Do_Class x_Toa_Do_Class207 = new();
		x_Toa_Do_Class207.Rxjh_Map = 40101;
		x_Toa_Do_Class207.Rxjh_X = 251f;
		x_Toa_Do_Class207.Rxjh_Y = 175f;
		x_Toa_Do_Class207.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class207);
		X_Toa_Do_Class x_Toa_Do_Class208 = new();
		x_Toa_Do_Class208.Rxjh_Map = 40101;
		x_Toa_Do_Class208.Rxjh_X = 215f;
		x_Toa_Do_Class208.Rxjh_Y = 97f;
		x_Toa_Do_Class208.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class208);
		X_Toa_Do_Class x_Toa_Do_Class209 = new();
		x_Toa_Do_Class209.Rxjh_Map = 40101;
		x_Toa_Do_Class209.Rxjh_X = 217f;
		x_Toa_Do_Class209.Rxjh_Y = 79f;
		x_Toa_Do_Class209.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class209);
		X_Toa_Do_Class x_Toa_Do_Class210 = new();
		x_Toa_Do_Class210.Rxjh_Map = 40101;
		x_Toa_Do_Class210.Rxjh_X = 227f;
		x_Toa_Do_Class210.Rxjh_Y = 77f;
		x_Toa_Do_Class210.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class210);
		X_Toa_Do_Class x_Toa_Do_Class211 = new();
		x_Toa_Do_Class211.Rxjh_Map = 40101;
		x_Toa_Do_Class211.Rxjh_X = 212f;
		x_Toa_Do_Class211.Rxjh_Y = 70f;
		x_Toa_Do_Class211.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class211);
		X_Toa_Do_Class x_Toa_Do_Class212 = new();
		x_Toa_Do_Class212.Rxjh_Map = 40101;
		x_Toa_Do_Class212.Rxjh_X = 200f;
		x_Toa_Do_Class212.Rxjh_Y = 70f;
		x_Toa_Do_Class212.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class212);
		X_Toa_Do_Class x_Toa_Do_Class213 = new();
		x_Toa_Do_Class213.Rxjh_Map = 40101;
		x_Toa_Do_Class213.Rxjh_X = 201f;
		x_Toa_Do_Class213.Rxjh_Y = 61f;
		x_Toa_Do_Class213.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class213);
		X_Toa_Do_Class x_Toa_Do_Class214 = new();
		x_Toa_Do_Class214.Rxjh_Map = 40101;
		x_Toa_Do_Class214.Rxjh_X = 213f;
		x_Toa_Do_Class214.Rxjh_Y = 58f;
		x_Toa_Do_Class214.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class214);
		X_Toa_Do_Class x_Toa_Do_Class215 = new();
		x_Toa_Do_Class215.Rxjh_Map = 40101;
		x_Toa_Do_Class215.Rxjh_X = 201f;
		x_Toa_Do_Class215.Rxjh_Y = 53f;
		x_Toa_Do_Class215.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class215);
		X_Toa_Do_Class x_Toa_Do_Class216 = new();
		x_Toa_Do_Class216.Rxjh_Map = 40101;
		x_Toa_Do_Class216.Rxjh_X = 215f;
		x_Toa_Do_Class216.Rxjh_Y = 55f;
		x_Toa_Do_Class216.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class216);
		X_Toa_Do_Class x_Toa_Do_Class217 = new();
		x_Toa_Do_Class217.Rxjh_Map = 40101;
		x_Toa_Do_Class217.Rxjh_X = 214f;
		x_Toa_Do_Class217.Rxjh_Y = 46f;
		x_Toa_Do_Class217.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class217);
		X_Toa_Do_Class x_Toa_Do_Class218 = new();
		x_Toa_Do_Class218.Rxjh_Map = 40101;
		x_Toa_Do_Class218.Rxjh_X = 202f;
		x_Toa_Do_Class218.Rxjh_Y = 45f;
		x_Toa_Do_Class218.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class218);
		X_Toa_Do_Class x_Toa_Do_Class219 = new();
		x_Toa_Do_Class219.Rxjh_Map = 40101;
		x_Toa_Do_Class219.Rxjh_X = 190f;
		x_Toa_Do_Class219.Rxjh_Y = 45f;
		x_Toa_Do_Class219.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class219);
		X_Toa_Do_Class x_Toa_Do_Class220 = new();
		x_Toa_Do_Class220.Rxjh_Map = 40101;
		x_Toa_Do_Class220.Rxjh_X = 193f;
		x_Toa_Do_Class220.Rxjh_Y = 24f;
		x_Toa_Do_Class220.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class220);
		X_Toa_Do_Class x_Toa_Do_Class221 = new();
		x_Toa_Do_Class221.Rxjh_Map = 40101;
		x_Toa_Do_Class221.Rxjh_X = 223f;
		x_Toa_Do_Class221.Rxjh_Y = 36f;
		x_Toa_Do_Class221.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class221);
		X_Toa_Do_Class x_Toa_Do_Class222 = new();
		x_Toa_Do_Class222.Rxjh_Map = 40101;
		x_Toa_Do_Class222.Rxjh_X = 231f;
		x_Toa_Do_Class222.Rxjh_Y = 33f;
		x_Toa_Do_Class222.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class222);
		X_Toa_Do_Class x_Toa_Do_Class223 = new();
		x_Toa_Do_Class223.Rxjh_Map = 40101;
		x_Toa_Do_Class223.Rxjh_X = 247f;
		x_Toa_Do_Class223.Rxjh_Y = 32f;
		x_Toa_Do_Class223.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class223);
		X_Toa_Do_Class x_Toa_Do_Class224 = new();
		x_Toa_Do_Class224.Rxjh_Map = 40101;
		x_Toa_Do_Class224.Rxjh_X = 217f;
		x_Toa_Do_Class224.Rxjh_Y = 43f;
		x_Toa_Do_Class224.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class224);
		X_Toa_Do_Class x_Toa_Do_Class225 = new();
		x_Toa_Do_Class225.Rxjh_Map = 40101;
		x_Toa_Do_Class225.Rxjh_X = 205f;
		x_Toa_Do_Class225.Rxjh_Y = 42f;
		x_Toa_Do_Class225.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class225);
		X_Toa_Do_Class x_Toa_Do_Class226 = new();
		x_Toa_Do_Class226.Rxjh_Map = 40101;
		x_Toa_Do_Class226.Rxjh_X = 206f;
		x_Toa_Do_Class226.Rxjh_Y = 25f;
		x_Toa_Do_Class226.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class226);
		X_Toa_Do_Class x_Toa_Do_Class227 = new();
		x_Toa_Do_Class227.Rxjh_Map = 40101;
		x_Toa_Do_Class227.Rxjh_X = 165f;
		x_Toa_Do_Class227.Rxjh_Y = 91f;
		x_Toa_Do_Class227.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class227);
		X_Toa_Do_Class x_Toa_Do_Class228 = new();
		x_Toa_Do_Class228.Rxjh_Map = 40101;
		x_Toa_Do_Class228.Rxjh_X = 178f;
		x_Toa_Do_Class228.Rxjh_Y = 92f;
		x_Toa_Do_Class228.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class228);
		X_Toa_Do_Class x_Toa_Do_Class229 = new();
		x_Toa_Do_Class229.Rxjh_Map = 40101;
		x_Toa_Do_Class229.Rxjh_X = 173f;
		x_Toa_Do_Class229.Rxjh_Y = 98f;
		x_Toa_Do_Class229.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class229);
		X_Toa_Do_Class x_Toa_Do_Class230 = new();
		x_Toa_Do_Class230.Rxjh_Map = 40101;
		x_Toa_Do_Class230.Rxjh_X = 187f;
		x_Toa_Do_Class230.Rxjh_Y = 105f;
		x_Toa_Do_Class230.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class230);
		X_Toa_Do_Class x_Toa_Do_Class231 = new();
		x_Toa_Do_Class231.Rxjh_Map = 40101;
		x_Toa_Do_Class231.Rxjh_X = 177f;
		x_Toa_Do_Class231.Rxjh_Y = 111f;
		x_Toa_Do_Class231.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class231);
		X_Toa_Do_Class x_Toa_Do_Class232 = new();
		x_Toa_Do_Class232.Rxjh_Map = 40101;
		x_Toa_Do_Class232.Rxjh_X = 175f;
		x_Toa_Do_Class232.Rxjh_Y = 121f;
		x_Toa_Do_Class232.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class232);
		X_Toa_Do_Class x_Toa_Do_Class233 = new();
		x_Toa_Do_Class35.Rxjh_Map = 40101;
		x_Toa_Do_Class35.Rxjh_X = 186f;
		x_Toa_Do_Class35.Rxjh_Y = 126f;
		x_Toa_Do_Class35.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class35);
		X_Toa_Do_Class x_Toa_Do_Class234 = new();
		x_Toa_Do_Class234.Rxjh_Map = 40101;
		x_Toa_Do_Class234.Rxjh_X = 172f;
		x_Toa_Do_Class234.Rxjh_Y = 131f;
		x_Toa_Do_Class234.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class234);
		X_Toa_Do_Class x_Toa_Do_Class235 = new();
		x_Toa_Do_Class235.Rxjh_Map = 40101;
		x_Toa_Do_Class235.Rxjh_X = 164f;
		x_Toa_Do_Class235.Rxjh_Y = 137f;
		x_Toa_Do_Class235.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class235);
		X_Toa_Do_Class x_Toa_Do_Class236 = new();
		x_Toa_Do_Class236.Rxjh_Map = 40101;
		x_Toa_Do_Class236.Rxjh_X = 160f;
		x_Toa_Do_Class236.Rxjh_Y = 146f;
		x_Toa_Do_Class236.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class236);
		X_Toa_Do_Class x_Toa_Do_Class237 = new();
		x_Toa_Do_Class237.Rxjh_Map = 40101;
		x_Toa_Do_Class237.Rxjh_X = 160f;
		x_Toa_Do_Class237.Rxjh_Y = 153f;
		x_Toa_Do_Class237.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class237);
		X_Toa_Do_Class x_Toa_Do_Class238 = new();
		x_Toa_Do_Class238.Rxjh_Map = 40101;
		x_Toa_Do_Class238.Rxjh_X = 148f;
		x_Toa_Do_Class238.Rxjh_Y = 59f;
		x_Toa_Do_Class238.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class238);
		X_Toa_Do_Class x_Toa_Do_Class239 = new();
		x_Toa_Do_Class239.Rxjh_Map = 40101;
		x_Toa_Do_Class239.Rxjh_X = 145f;
		x_Toa_Do_Class239.Rxjh_Y = 45f;
		x_Toa_Do_Class239.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class239);
		X_Toa_Do_Class x_Toa_Do_Class240 = new();
		x_Toa_Do_Class240.Rxjh_Map = 40101;
		x_Toa_Do_Class240.Rxjh_X = 135f;
		x_Toa_Do_Class240.Rxjh_Y = 46f;
		x_Toa_Do_Class240.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class240);
		X_Toa_Do_Class x_Toa_Do_Class241 = new();
		x_Toa_Do_Class241.Rxjh_Map = 40101;
		x_Toa_Do_Class241.Rxjh_X = 127f;
		x_Toa_Do_Class241.Rxjh_Y = 46f;
		x_Toa_Do_Class241.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class241);
		X_Toa_Do_Class x_Toa_Do_Class242 = new();
		x_Toa_Do_Class242.Rxjh_Map = 40101;
		x_Toa_Do_Class242.Rxjh_X = 138f;
		x_Toa_Do_Class242.Rxjh_Y = 27f;
		x_Toa_Do_Class242.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class242);
		X_Toa_Do_Class x_Toa_Do_Class243 = new();
		x_Toa_Do_Class243.Rxjh_Map = 40101;
		x_Toa_Do_Class243.Rxjh_X = 131f;
		x_Toa_Do_Class243.Rxjh_Y = 28f;
		x_Toa_Do_Class243.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class243);
		X_Toa_Do_Class x_Toa_Do_Class244 = new();
		x_Toa_Do_Class244.Rxjh_Map = 40101;
		x_Toa_Do_Class244.Rxjh_X = 138f;
		x_Toa_Do_Class244.Rxjh_Y = 185f;
		x_Toa_Do_Class244.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class244);
		X_Toa_Do_Class x_Toa_Do_Class245 = new();
		x_Toa_Do_Class245.Rxjh_Map = 40101;
		x_Toa_Do_Class245.Rxjh_X = 124f;
		x_Toa_Do_Class245.Rxjh_Y = 27f;
		x_Toa_Do_Class245.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class245);
		X_Toa_Do_Class x_Toa_Do_Class246 = new();
		x_Toa_Do_Class246.Rxjh_Map = 40101;
		x_Toa_Do_Class246.Rxjh_X = 104f;
		x_Toa_Do_Class246.Rxjh_Y = 20f;
		x_Toa_Do_Class246.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class246);
		X_Toa_Do_Class x_Toa_Do_Class247 = new();
		x_Toa_Do_Class247.Rxjh_Map = 40101;
		x_Toa_Do_Class247.Rxjh_X = 106f;
		x_Toa_Do_Class247.Rxjh_Y = 31f;
		x_Toa_Do_Class247.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class247);
		X_Toa_Do_Class x_Toa_Do_Class248 = new();
		x_Toa_Do_Class248.Rxjh_Map = 40101;
		x_Toa_Do_Class248.Rxjh_X = 106f;
		x_Toa_Do_Class248.Rxjh_Y = 41f;
		x_Toa_Do_Class248.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class248);
		X_Toa_Do_Class x_Toa_Do_Class249 = new();
		x_Toa_Do_Class249.Rxjh_Map = 40101;
		x_Toa_Do_Class249.Rxjh_X = 118f;
		x_Toa_Do_Class249.Rxjh_Y = 46f;
		x_Toa_Do_Class249.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class249);
		X_Toa_Do_Class x_Toa_Do_Class250 = new();
		x_Toa_Do_Class250.Rxjh_Map = 40101;
		x_Toa_Do_Class250.Rxjh_X = 104f;
		x_Toa_Do_Class250.Rxjh_Y = 20f;
		x_Toa_Do_Class250.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class250);
		X_Toa_Do_Class x_Toa_Do_Class251 = new();
		x_Toa_Do_Class251.Rxjh_Map = 40101;
		x_Toa_Do_Class251.Rxjh_X = 105f;
		x_Toa_Do_Class251.Rxjh_Y = 32f;
		x_Toa_Do_Class251.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class251);
		X_Toa_Do_Class x_Toa_Do_Class252 = new();
		x_Toa_Do_Class252.Rxjh_Map = 40101;
		x_Toa_Do_Class252.Rxjh_X = 107f;
		x_Toa_Do_Class252.Rxjh_Y = 44f;
		x_Toa_Do_Class252.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class252);
		X_Toa_Do_Class x_Toa_Do_Class253 = new();
		x_Toa_Do_Class253.Rxjh_Map = 40101;
		x_Toa_Do_Class253.Rxjh_X = 98f;
		x_Toa_Do_Class253.Rxjh_Y = 48f;
		x_Toa_Do_Class253.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class253);
		X_Toa_Do_Class x_Toa_Do_Class254 = new();
		x_Toa_Do_Class254.Rxjh_Map = 40101;
		x_Toa_Do_Class254.Rxjh_X = 92f;
		x_Toa_Do_Class254.Rxjh_Y = 47f;
		x_Toa_Do_Class254.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class254);
		X_Toa_Do_Class x_Toa_Do_Class255 = new();
		x_Toa_Do_Class255.Rxjh_Map = 40101;
		x_Toa_Do_Class255.Rxjh_X = 107f;
		x_Toa_Do_Class255.Rxjh_Y = 66f;
		x_Toa_Do_Class255.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class255);
		X_Toa_Do_Class x_Toa_Do_Class256 = new();
		x_Toa_Do_Class256.Rxjh_Map = 40101;
		x_Toa_Do_Class256.Rxjh_X = 109f;
		x_Toa_Do_Class256.Rxjh_Y = 75f;
		x_Toa_Do_Class256.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class256);
		X_Toa_Do_Class x_Toa_Do_Class257 = new();
		x_Toa_Do_Class257.Rxjh_Map = 40101;
		x_Toa_Do_Class257.Rxjh_X = 123f;
		x_Toa_Do_Class257.Rxjh_Y = 79f;
		x_Toa_Do_Class257.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class257);
		X_Toa_Do_Class x_Toa_Do_Class258 = new();
		x_Toa_Do_Class258.Rxjh_Map = 40101;
		x_Toa_Do_Class258.Rxjh_X = 123f;
		x_Toa_Do_Class258.Rxjh_Y = 89f;
		x_Toa_Do_Class258.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class258);
		X_Toa_Do_Class x_Toa_Do_Class259 = new();
		x_Toa_Do_Class259.Rxjh_Map = 40101;
		x_Toa_Do_Class259.Rxjh_X = 185f;
		x_Toa_Do_Class259.Rxjh_Y = 138f;
		x_Toa_Do_Class259.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class259);
		X_Toa_Do_Class x_Toa_Do_Class260 = new();
		x_Toa_Do_Class260.Rxjh_Map = 40101;
		x_Toa_Do_Class260.Rxjh_X = 112f;
		x_Toa_Do_Class260.Rxjh_Y = 94f;
		x_Toa_Do_Class260.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class260);
		X_Toa_Do_Class x_Toa_Do_Class261 = new();
		x_Toa_Do_Class261.Rxjh_Map = 40101;
		x_Toa_Do_Class261.Rxjh_X = 100f;
		x_Toa_Do_Class261.Rxjh_Y = 95f;
		x_Toa_Do_Class261.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class261);
		X_Toa_Do_Class x_Toa_Do_Class262 = new();
		x_Toa_Do_Class262.Rxjh_Map = 40101;
		x_Toa_Do_Class262.Rxjh_X = 112f;
		x_Toa_Do_Class262.Rxjh_Y = 104f;
		x_Toa_Do_Class262.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class262);
		X_Toa_Do_Class x_Toa_Do_Class263 = new();
		x_Toa_Do_Class263.Rxjh_Map = 40101;
		x_Toa_Do_Class263.Rxjh_X = 111f;
		x_Toa_Do_Class263.Rxjh_Y = 120f;
		x_Toa_Do_Class263.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class263);
		X_Toa_Do_Class x_Toa_Do_Class264 = new();
		x_Toa_Do_Class264.Rxjh_Map = 40101;
		x_Toa_Do_Class264.Rxjh_X = 73f;
		x_Toa_Do_Class264.Rxjh_Y = 119f;
		x_Toa_Do_Class264.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class264);
		X_Toa_Do_Class x_Toa_Do_Class265 = new();
		x_Toa_Do_Class265.Rxjh_Map = 40101;
		x_Toa_Do_Class265.Rxjh_X = 62f;
		x_Toa_Do_Class265.Rxjh_Y = 120f;
		x_Toa_Do_Class265.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class265);
		X_Toa_Do_Class x_Toa_Do_Class266 = new();
		x_Toa_Do_Class266.Rxjh_Map = 40101;
		x_Toa_Do_Class266.Rxjh_X = 62f;
		x_Toa_Do_Class266.Rxjh_Y = 128f;
		x_Toa_Do_Class266.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class266);
		X_Toa_Do_Class x_Toa_Do_Class267 = new();
		x_Toa_Do_Class267.Rxjh_Map = 40101;
		x_Toa_Do_Class267.Rxjh_X = 62f;
		x_Toa_Do_Class267.Rxjh_Y = 139f;
		x_Toa_Do_Class267.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class267);
		X_Toa_Do_Class x_Toa_Do_Class268 = new();
		x_Toa_Do_Class268.Rxjh_Map = 40101;
		x_Toa_Do_Class268.Rxjh_X = 62f;
		x_Toa_Do_Class268.Rxjh_Y = 146f;
		x_Toa_Do_Class268.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class268);
		X_Toa_Do_Class x_Toa_Do_Class269 = new();
		x_Toa_Do_Class269.Rxjh_Map = 40101;
		x_Toa_Do_Class269.Rxjh_X = 62f;
		x_Toa_Do_Class269.Rxjh_Y = 158f;
		x_Toa_Do_Class269.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class269);
		X_Toa_Do_Class x_Toa_Do_Class270 = new();
		x_Toa_Do_Class270.Rxjh_Map = 40101;
		x_Toa_Do_Class270.Rxjh_X = 62f;
		x_Toa_Do_Class270.Rxjh_Y = 166f;
		x_Toa_Do_Class270.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class270);
		X_Toa_Do_Class x_Toa_Do_Class271 = new();
		x_Toa_Do_Class271.Rxjh_Map = 40101;
		x_Toa_Do_Class271.Rxjh_X = 62f;
		x_Toa_Do_Class271.Rxjh_Y = 173f;
		x_Toa_Do_Class271.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class271);
		X_Toa_Do_Class x_Toa_Do_Class272 = new();
		x_Toa_Do_Class272.Rxjh_Map = 40101;
		x_Toa_Do_Class272.Rxjh_X = 62f;
		x_Toa_Do_Class272.Rxjh_Y = 182f;
		x_Toa_Do_Class272.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class272);
		X_Toa_Do_Class x_Toa_Do_Class273 = new();
		x_Toa_Do_Class273.Rxjh_Map = 40101;
		x_Toa_Do_Class273.Rxjh_X = 190f;
		x_Toa_Do_Class273.Rxjh_Y = 42f;
		x_Toa_Do_Class273.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class273);
		X_Toa_Do_Class x_Toa_Do_Class274 = new();
		x_Toa_Do_Class274.Rxjh_Map = 40101;
		x_Toa_Do_Class274.Rxjh_X = 73f;
		x_Toa_Do_Class274.Rxjh_Y = 131f;
		x_Toa_Do_Class274.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class274);
		X_Toa_Do_Class x_Toa_Do_Class275 = new();
		x_Toa_Do_Class275.Rxjh_Map = 40101;
		x_Toa_Do_Class275.Rxjh_X = 73f;
		x_Toa_Do_Class275.Rxjh_Y = 140f;
		x_Toa_Do_Class275.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class275);
		X_Toa_Do_Class x_Toa_Do_Class276 = new();
		x_Toa_Do_Class276.Rxjh_Map = 40101;
		x_Toa_Do_Class276.Rxjh_X = 73f;
		x_Toa_Do_Class276.Rxjh_Y = 149f;
		x_Toa_Do_Class276.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class276);
		X_Toa_Do_Class x_Toa_Do_Class277 = new();
		x_Toa_Do_Class277.Rxjh_Map = 40101;
		x_Toa_Do_Class277.Rxjh_X = 73f;
		x_Toa_Do_Class277.Rxjh_Y = 148f;
		x_Toa_Do_Class277.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class277);
		X_Toa_Do_Class x_Toa_Do_Class278 = new();
		x_Toa_Do_Class278.Rxjh_Map = 40101;
		x_Toa_Do_Class278.Rxjh_X = -65f;
		x_Toa_Do_Class278.Rxjh_Y = 119f;
		x_Toa_Do_Class278.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class278);
		X_Toa_Do_Class x_Toa_Do_Class279 = new();
		x_Toa_Do_Class279.Rxjh_Map = 40101;
		x_Toa_Do_Class279.Rxjh_X = -65f;
		x_Toa_Do_Class279.Rxjh_Y = 131f;
		x_Toa_Do_Class279.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class279);
		X_Toa_Do_Class x_Toa_Do_Class280 = new();
		x_Toa_Do_Class280.Rxjh_Map = 40101;
		x_Toa_Do_Class280.Rxjh_X = -65f;
		x_Toa_Do_Class280.Rxjh_Y = 145f;
		x_Toa_Do_Class280.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class280);
		X_Toa_Do_Class x_Toa_Do_Class281 = new();
		x_Toa_Do_Class281.Rxjh_Map = 40101;
		x_Toa_Do_Class281.Rxjh_X = -65f;
		x_Toa_Do_Class281.Rxjh_Y = 158f;
		x_Toa_Do_Class281.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class281);
		X_Toa_Do_Class x_Toa_Do_Class282 = new();
		x_Toa_Do_Class282.Rxjh_Map = 40101;
		x_Toa_Do_Class282.Rxjh_X = -65f;
		x_Toa_Do_Class282.Rxjh_Y = 167f;
		x_Toa_Do_Class282.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class282);
		X_Toa_Do_Class x_Toa_Do_Class283 = new();
		x_Toa_Do_Class283.Rxjh_Map = 40101;
		x_Toa_Do_Class283.Rxjh_X = -65f;
		x_Toa_Do_Class283.Rxjh_Y = 175f;
		x_Toa_Do_Class283.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class283);
		X_Toa_Do_Class x_Toa_Do_Class284 = new();
		x_Toa_Do_Class284.Rxjh_Map = 40101;
		x_Toa_Do_Class284.Rxjh_X = -65f;
		x_Toa_Do_Class284.Rxjh_Y = 183f;
		x_Toa_Do_Class284.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class284);
		X_Toa_Do_Class x_Toa_Do_Class285 = new();
		x_Toa_Do_Class285.Rxjh_Map = 40101;
		x_Toa_Do_Class285.Rxjh_X = -75f;
		x_Toa_Do_Class285.Rxjh_Y = 185f;
		x_Toa_Do_Class285.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class285);
		X_Toa_Do_Class x_Toa_Do_Class286 = new();
		x_Toa_Do_Class286.Rxjh_Map = 40101;
		x_Toa_Do_Class286.Rxjh_X = -75f;
		x_Toa_Do_Class286.Rxjh_Y = 178f;
		x_Toa_Do_Class286.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class286);
		X_Toa_Do_Class x_Toa_Do_Class287 = new();
		x_Toa_Do_Class287.Rxjh_Map = 40101;
		x_Toa_Do_Class287.Rxjh_X = -75f;
		x_Toa_Do_Class287.Rxjh_Y = 167f;
		x_Toa_Do_Class287.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class287);
		X_Toa_Do_Class x_Toa_Do_Class288 = new();
		x_Toa_Do_Class288.Rxjh_Map = 40101;
		x_Toa_Do_Class288.Rxjh_X = -75f;
		x_Toa_Do_Class288.Rxjh_Y = 157f;
		x_Toa_Do_Class288.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class288);
		X_Toa_Do_Class x_Toa_Do_Class289 = new();
		x_Toa_Do_Class289.Rxjh_Map = 40101;
		x_Toa_Do_Class289.Rxjh_X = -75f;
		x_Toa_Do_Class289.Rxjh_Y = 148f;
		x_Toa_Do_Class289.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class289);
		X_Toa_Do_Class x_Toa_Do_Class290 = new();
		x_Toa_Do_Class290.Rxjh_Map = 40101;
		x_Toa_Do_Class290.Rxjh_X = -75f;
		x_Toa_Do_Class290.Rxjh_Y = 137f;
		x_Toa_Do_Class290.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class290);
		X_Toa_Do_Class x_Toa_Do_Class291 = new();
		x_Toa_Do_Class291.Rxjh_Map = 40101;
		x_Toa_Do_Class291.Rxjh_X = -75f;
		x_Toa_Do_Class291.Rxjh_Y = 126f;
		x_Toa_Do_Class291.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class291);
		X_Toa_Do_Class x_Toa_Do_Class292 = new();
		x_Toa_Do_Class292.Rxjh_Map = 40101;
		x_Toa_Do_Class292.Rxjh_X = -75f;
		x_Toa_Do_Class292.Rxjh_Y = 119f;
		x_Toa_Do_Class292.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class292);
		X_Toa_Do_Class x_Toa_Do_Class293 = new();
		x_Toa_Do_Class293.Rxjh_Map = 40101;
		x_Toa_Do_Class293.Rxjh_X = -110f;
		x_Toa_Do_Class293.Rxjh_Y = 118f;
		x_Toa_Do_Class293.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class293);
		X_Toa_Do_Class x_Toa_Do_Class294 = new();
		x_Toa_Do_Class294.Rxjh_Map = 40101;
		x_Toa_Do_Class294.Rxjh_X = -12f;
		x_Toa_Do_Class294.Rxjh_Y = 101f;
		x_Toa_Do_Class294.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class294);
		X_Toa_Do_Class x_Toa_Do_Class295 = new();
		x_Toa_Do_Class295.Rxjh_Map = 40101;
		x_Toa_Do_Class295.Rxjh_X = -112f;
		x_Toa_Do_Class295.Rxjh_Y = 91f;
		x_Toa_Do_Class295.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class295);
		X_Toa_Do_Class x_Toa_Do_Class296 = new();
		x_Toa_Do_Class296.Rxjh_Map = 40101;
		x_Toa_Do_Class296.Rxjh_X = -125f;
		x_Toa_Do_Class296.Rxjh_Y = 92f;
		x_Toa_Do_Class296.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class296);
		X_Toa_Do_Class x_Toa_Do_Class297 = new();
		x_Toa_Do_Class297.Rxjh_Map = 40101;
		x_Toa_Do_Class297.Rxjh_X = -110f;
		x_Toa_Do_Class297.Rxjh_Y = 84f;
		x_Toa_Do_Class297.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class297);
		X_Toa_Do_Class x_Toa_Do_Class298 = new();
		x_Toa_Do_Class298.Rxjh_Map = 40101;
		x_Toa_Do_Class298.Rxjh_X = 101f;
		x_Toa_Do_Class298.Rxjh_Y = 81f;
		x_Toa_Do_Class298.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class298);
		X_Toa_Do_Class x_Toa_Do_Class299 = new();
		x_Toa_Do_Class299.Rxjh_Map = 40101;
		x_Toa_Do_Class299.Rxjh_X = -113f;
		x_Toa_Do_Class299.Rxjh_Y = 78f;
		x_Toa_Do_Class299.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class299);
		X_Toa_Do_Class x_Toa_Do_Class300 = new();
		x_Toa_Do_Class300.Rxjh_Map = 40101;
		x_Toa_Do_Class300.Rxjh_X = -99f;
		x_Toa_Do_Class300.Rxjh_Y = 74f;
		x_Toa_Do_Class300.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class300);
		X_Toa_Do_Class x_Toa_Do_Class301 = new();
		x_Toa_Do_Class301.Rxjh_Map = 40101;
		x_Toa_Do_Class301.Rxjh_X = -88f;
		x_Toa_Do_Class301.Rxjh_Y = 72f;
		x_Toa_Do_Class301.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class301);
		X_Toa_Do_Class x_Toa_Do_Class302 = new();
		x_Toa_Do_Class302.Rxjh_Map = 40101;
		x_Toa_Do_Class302.Rxjh_X = -101f;
		x_Toa_Do_Class302.Rxjh_Y = 69f;
		x_Toa_Do_Class302.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class302);
		X_Toa_Do_Class x_Toa_Do_Class303 = new();
		x_Toa_Do_Class303.Rxjh_Map = 40101;
		x_Toa_Do_Class303.Rxjh_X = -112f;
		x_Toa_Do_Class303.Rxjh_Y = 58f;
		x_Toa_Do_Class303.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class303);
		X_Toa_Do_Class x_Toa_Do_Class304 = new();
		x_Toa_Do_Class304.Rxjh_Map = 40101;
		x_Toa_Do_Class304.Rxjh_X = -112f;
		x_Toa_Do_Class304.Rxjh_Y = 48f;
		x_Toa_Do_Class304.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class304);
		X_Toa_Do_Class x_Toa_Do_Class305 = new();
		x_Toa_Do_Class305.Rxjh_Map = 40101;
		x_Toa_Do_Class305.Rxjh_X = -120f;
		x_Toa_Do_Class305.Rxjh_Y = 45f;
		x_Toa_Do_Class305.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class305);
		X_Toa_Do_Class x_Toa_Do_Class306 = new();
		x_Toa_Do_Class306.Rxjh_Map = 40101;
		x_Toa_Do_Class306.Rxjh_X = -128f;
		x_Toa_Do_Class306.Rxjh_Y = 43f;
		x_Toa_Do_Class306.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class306);
		X_Toa_Do_Class x_Toa_Do_Class307 = new();
		x_Toa_Do_Class307.Rxjh_Map = 40101;
		x_Toa_Do_Class307.Rxjh_X = -90f;
		x_Toa_Do_Class307.Rxjh_Y = 43f;
		x_Toa_Do_Class307.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class307);
		X_Toa_Do_Class x_Toa_Do_Class308 = new();
		x_Toa_Do_Class308.Rxjh_Map = 40101;
		x_Toa_Do_Class308.Rxjh_X = -86f;
		x_Toa_Do_Class308.Rxjh_Y = 34f;
		x_Toa_Do_Class308.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class308);
		X_Toa_Do_Class x_Toa_Do_Class309 = new();
		x_Toa_Do_Class309.Rxjh_Map = 40101;
		x_Toa_Do_Class309.Rxjh_X = -74f;
		x_Toa_Do_Class309.Rxjh_Y = 34f;
		x_Toa_Do_Class309.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class309);
		X_Toa_Do_Class x_Toa_Do_Class310 = new();
		x_Toa_Do_Class310.Rxjh_Map = 40101;
		x_Toa_Do_Class310.Rxjh_X = -149f;
		x_Toa_Do_Class310.Rxjh_Y = 61f;
		x_Toa_Do_Class310.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class310);
		X_Toa_Do_Class x_Toa_Do_Class311 = new();
		x_Toa_Do_Class311.Rxjh_Map = 40101;
		x_Toa_Do_Class311.Rxjh_X = -158f;
		x_Toa_Do_Class311.Rxjh_Y = 63f;
		x_Toa_Do_Class311.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class311);
		X_Toa_Do_Class x_Toa_Do_Class312 = new();
		x_Toa_Do_Class312.Rxjh_Map = 40101;
		x_Toa_Do_Class312.Rxjh_X = -165f;
		x_Toa_Do_Class312.Rxjh_Y = 71f;
		x_Toa_Do_Class312.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class312);
		X_Toa_Do_Class x_Toa_Do_Class313 = new();
		x_Toa_Do_Class313.Rxjh_Map = 40101;
		x_Toa_Do_Class313.Rxjh_X = 173f;
		x_Toa_Do_Class313.Rxjh_Y = 59f;
		x_Toa_Do_Class313.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class313);
		X_Toa_Do_Class x_Toa_Do_Class314 = new();
		x_Toa_Do_Class314.Rxjh_Map = 40101;
		x_Toa_Do_Class314.Rxjh_X = -174f;
		x_Toa_Do_Class314.Rxjh_Y = 70f;
		x_Toa_Do_Class314.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class314);
		X_Toa_Do_Class x_Toa_Do_Class315 = new();
		x_Toa_Do_Class315.Rxjh_Map = 40101;
		x_Toa_Do_Class315.Rxjh_X = -173f;
		x_Toa_Do_Class315.Rxjh_Y = 43f;
		x_Toa_Do_Class315.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class315);
		X_Toa_Do_Class x_Toa_Do_Class316 = new();
		x_Toa_Do_Class316.Rxjh_Map = 40101;
		x_Toa_Do_Class316.Rxjh_X = -163f;
		x_Toa_Do_Class316.Rxjh_Y = 42f;
		x_Toa_Do_Class316.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class316);
		X_Toa_Do_Class x_Toa_Do_Class317 = new();
		x_Toa_Do_Class317.Rxjh_Map = 40101;
		x_Toa_Do_Class317.Rxjh_X = -173f;
		x_Toa_Do_Class317.Rxjh_Y = 41f;
		x_Toa_Do_Class317.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class35);
		X_Toa_Do_Class x_Toa_Do_Class318 = new();
		x_Toa_Do_Class318.Rxjh_Map = 40101;
		x_Toa_Do_Class318.Rxjh_X = -181f;
		x_Toa_Do_Class318.Rxjh_Y = 35f;
		x_Toa_Do_Class318.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class318);
		X_Toa_Do_Class x_Toa_Do_Class319 = new();
		x_Toa_Do_Class319.Rxjh_Map = 40101;
		x_Toa_Do_Class319.Rxjh_X = -194f;
		x_Toa_Do_Class319.Rxjh_Y = 42f;
		x_Toa_Do_Class319.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class319);
		X_Toa_Do_Class x_Toa_Do_Class320 = new();
		x_Toa_Do_Class320.Rxjh_Map = 40101;
		x_Toa_Do_Class320.Rxjh_X = -204f;
		x_Toa_Do_Class320.Rxjh_Y = 42f;
		x_Toa_Do_Class320.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class320);
		X_Toa_Do_Class x_Toa_Do_Class321 = new();
		x_Toa_Do_Class321.Rxjh_Map = 40101;
		x_Toa_Do_Class321.Rxjh_X = -224f;
		x_Toa_Do_Class321.Rxjh_Y = 89f;
		x_Toa_Do_Class321.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class321);
		X_Toa_Do_Class x_Toa_Do_Class322 = new();
		x_Toa_Do_Class322.Rxjh_Map = 40101;
		x_Toa_Do_Class322.Rxjh_X = -235f;
		x_Toa_Do_Class322.Rxjh_Y = 89f;
		x_Toa_Do_Class322.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class322);
		X_Toa_Do_Class x_Toa_Do_Class323 = new();
		x_Toa_Do_Class323.Rxjh_Map = 40101;
		x_Toa_Do_Class323.Rxjh_X = -246f;
		x_Toa_Do_Class323.Rxjh_Y = 89f;
		x_Toa_Do_Class323.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class323);
		X_Toa_Do_Class x_Toa_Do_Class324 = new();
		x_Toa_Do_Class324.Rxjh_Map = 40101;
		x_Toa_Do_Class324.Rxjh_X = -252f;
		x_Toa_Do_Class324.Rxjh_Y = 94f;
		x_Toa_Do_Class324.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class324);
		X_Toa_Do_Class x_Toa_Do_Class325 = new();
		x_Toa_Do_Class325.Rxjh_Map = 40101;
		x_Toa_Do_Class325.Rxjh_X = -247f;
		x_Toa_Do_Class325.Rxjh_Y = 102f;
		x_Toa_Do_Class325.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class325);
		X_Toa_Do_Class x_Toa_Do_Class326 = new();
		x_Toa_Do_Class326.Rxjh_Map = 40101;
		x_Toa_Do_Class326.Rxjh_X = -260f;
		x_Toa_Do_Class326.Rxjh_Y = 100f;
		x_Toa_Do_Class326.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class326);
		X_Toa_Do_Class x_Toa_Do_Class327 = new();
		x_Toa_Do_Class327.Rxjh_Map = 40101;
		x_Toa_Do_Class327.Rxjh_X = -250f;
		x_Toa_Do_Class327.Rxjh_Y = 82f;
		x_Toa_Do_Class327.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class327);
		X_Toa_Do_Class x_Toa_Do_Class328 = new();
		x_Toa_Do_Class328.Rxjh_Map = 40101;
		x_Toa_Do_Class328.Rxjh_X = -249f;
		x_Toa_Do_Class328.Rxjh_Y = 70f;
		x_Toa_Do_Class328.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class328);
		X_Toa_Do_Class x_Toa_Do_Class329 = new();
		x_Toa_Do_Class329.Rxjh_Map = 40101;
		x_Toa_Do_Class329.Rxjh_X = -246f;
		x_Toa_Do_Class329.Rxjh_Y = 62f;
		x_Toa_Do_Class329.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class329);
		X_Toa_Do_Class x_Toa_Do_Class330 = new();
		x_Toa_Do_Class330.Rxjh_Map = 40101;
		x_Toa_Do_Class330.Rxjh_X = -236f;
		x_Toa_Do_Class330.Rxjh_Y = 62f;
		x_Toa_Do_Class330.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class330);
		X_Toa_Do_Class x_Toa_Do_Class331 = new();
		x_Toa_Do_Class331.Rxjh_Map = 40101;
		x_Toa_Do_Class331.Rxjh_X = -253f;
		x_Toa_Do_Class331.Rxjh_Y = 54f;
		x_Toa_Do_Class331.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class331);
		X_Toa_Do_Class x_Toa_Do_Class332 = new();
		x_Toa_Do_Class332.Rxjh_Map = 40101;
		x_Toa_Do_Class332.Rxjh_X = -260f;
		x_Toa_Do_Class332.Rxjh_Y = 49f;
		x_Toa_Do_Class332.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class332);
		X_Toa_Do_Class x_Toa_Do_Class333 = new();
		x_Toa_Do_Class333.Rxjh_Map = 40101;
		x_Toa_Do_Class333.Rxjh_X = -266f;
		x_Toa_Do_Class333.Rxjh_Y = 48f;
		x_Toa_Do_Class333.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class333);
		X_Toa_Do_Class x_Toa_Do_Class334 = new();
		x_Toa_Do_Class334.Rxjh_Map = 40101;
		x_Toa_Do_Class334.Rxjh_X = -244f;
		x_Toa_Do_Class334.Rxjh_Y = 135f;
		x_Toa_Do_Class334.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class334);
		X_Toa_Do_Class x_Toa_Do_Class335 = new();
		x_Toa_Do_Class335.Rxjh_Map = 40101;
		x_Toa_Do_Class335.Rxjh_X = -244f;
		x_Toa_Do_Class335.Rxjh_Y = 144f;
		x_Toa_Do_Class335.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class335);
		X_Toa_Do_Class x_Toa_Do_Class336 = new();
		x_Toa_Do_Class336.Rxjh_Map = 40101;
		x_Toa_Do_Class336.Rxjh_X = -235f;
		x_Toa_Do_Class336.Rxjh_Y = 148f;
		x_Toa_Do_Class336.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class336);
		X_Toa_Do_Class x_Toa_Do_Class337 = new();
		x_Toa_Do_Class337.Rxjh_Map = 40101;
		x_Toa_Do_Class337.Rxjh_X = -234f;
		x_Toa_Do_Class337.Rxjh_Y = 157f;
		x_Toa_Do_Class337.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class337);
		X_Toa_Do_Class x_Toa_Do_Class338 = new();
		x_Toa_Do_Class338.Rxjh_Map = 40101;
		x_Toa_Do_Class338.Rxjh_X = -234f;
		x_Toa_Do_Class338.Rxjh_Y = 164f;
		x_Toa_Do_Class338.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class338);
		X_Toa_Do_Class x_Toa_Do_Class339 = new();
		x_Toa_Do_Class339.Rxjh_Map = 40101;
		x_Toa_Do_Class339.Rxjh_X = -187f;
		x_Toa_Do_Class339.Rxjh_Y = 73f;
		x_Toa_Do_Class339.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class339);
		X_Toa_Do_Class x_Toa_Do_Class340 = new();
		x_Toa_Do_Class340.Rxjh_Map = 40101;
		x_Toa_Do_Class340.Rxjh_X = -191f;
		x_Toa_Do_Class340.Rxjh_Y = 74f;
		x_Toa_Do_Class340.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class340);
		X_Toa_Do_Class x_Toa_Do_Class341 = new();
		x_Toa_Do_Class341.Rxjh_Map = 40101;
		x_Toa_Do_Class341.Rxjh_X = -206f;
		x_Toa_Do_Class341.Rxjh_Y = 69f;
		x_Toa_Do_Class341.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class341);
		X_Toa_Do_Class x_Toa_Do_Class342 = new();
		x_Toa_Do_Class342.Rxjh_Map = 40101;
		x_Toa_Do_Class342.Rxjh_X = -190f;
		x_Toa_Do_Class342.Rxjh_Y = 81f;
		x_Toa_Do_Class342.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class342);
		X_Toa_Do_Class x_Toa_Do_Class343 = new();
		x_Toa_Do_Class343.Rxjh_Map = 40101;
		x_Toa_Do_Class343.Rxjh_X = -188f;
		x_Toa_Do_Class343.Rxjh_Y = 94f;
		x_Toa_Do_Class343.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class343);
		X_Toa_Do_Class x_Toa_Do_Class344 = new();
		x_Toa_Do_Class344.Rxjh_Map = 40101;
		x_Toa_Do_Class344.Rxjh_X = -173f;
		x_Toa_Do_Class344.Rxjh_Y = 100f;
		x_Toa_Do_Class344.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class344);
		X_Toa_Do_Class x_Toa_Do_Class345 = new();
		x_Toa_Do_Class345.Rxjh_Map = 40101;
		x_Toa_Do_Class345.Rxjh_X = -169f;
		x_Toa_Do_Class345.Rxjh_Y = 108f;
		x_Toa_Do_Class345.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class345);
		X_Toa_Do_Class x_Toa_Do_Class346 = new();
		x_Toa_Do_Class346.Rxjh_Map = 40101;
		x_Toa_Do_Class346.Rxjh_X = -164f;
		x_Toa_Do_Class346.Rxjh_Y = 111f;
		x_Toa_Do_Class346.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class346);
		X_Toa_Do_Class x_Toa_Do_Class347 = new();
		x_Toa_Do_Class347.Rxjh_Map = 40101;
		x_Toa_Do_Class347.Rxjh_X = -155f;
		x_Toa_Do_Class347.Rxjh_Y = 110f;
		x_Toa_Do_Class347.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class347);
		X_Toa_Do_Class x_Toa_Do_Class348 = new();
		x_Toa_Do_Class348.Rxjh_Map = 40101;
		x_Toa_Do_Class348.Rxjh_X = -169f;
		x_Toa_Do_Class348.Rxjh_Y = 113f;
		x_Toa_Do_Class348.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class348);
		X_Toa_Do_Class x_Toa_Do_Class349 = new();
		x_Toa_Do_Class349.Rxjh_Map = 40101;
		x_Toa_Do_Class349.Rxjh_X = -170f;
		x_Toa_Do_Class349.Rxjh_Y = 131f;
		x_Toa_Do_Class349.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class349);
		X_Toa_Do_Class x_Toa_Do_Class350 = new();
		x_Toa_Do_Class350.Rxjh_Map = 40101;
		x_Toa_Do_Class350.Rxjh_X = -155f;
		x_Toa_Do_Class350.Rxjh_Y = 132f;
		x_Toa_Do_Class350.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class350);
		X_Toa_Do_Class x_Toa_Do_Class351 = new();
		x_Toa_Do_Class351.Rxjh_Map = 40101;
		x_Toa_Do_Class351.Rxjh_X = -154f;
		x_Toa_Do_Class351.Rxjh_Y = 142f;
		x_Toa_Do_Class351.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class351);
		X_Toa_Do_Class x_Toa_Do_Class352 = new();
		x_Toa_Do_Class352.Rxjh_Map = 40101;
		x_Toa_Do_Class352.Rxjh_X = -302f;
		x_Toa_Do_Class352.Rxjh_Y = -276f;
		x_Toa_Do_Class352.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class352);
		X_Toa_Do_Class x_Toa_Do_Class353 = new();
		x_Toa_Do_Class353.Rxjh_Map = 40101;
		x_Toa_Do_Class353.Rxjh_X = -302f;
		x_Toa_Do_Class353.Rxjh_Y = -285f;
		x_Toa_Do_Class353.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class353);
		X_Toa_Do_Class x_Toa_Do_Class354 = new();
		x_Toa_Do_Class354.Rxjh_Map = 40101;
		x_Toa_Do_Class354.Rxjh_X = -302f;
		x_Toa_Do_Class354.Rxjh_Y = -294f;
		x_Toa_Do_Class354.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class354);
		X_Toa_Do_Class x_Toa_Do_Class355 = new();
		x_Toa_Do_Class355.Rxjh_Map = 40101;
		x_Toa_Do_Class355.Rxjh_X = -302f;
		x_Toa_Do_Class355.Rxjh_Y = -303f;
		x_Toa_Do_Class355.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class355);
		X_Toa_Do_Class x_Toa_Do_Class356 = new();
		x_Toa_Do_Class356.Rxjh_Map = 40101;
		x_Toa_Do_Class356.Rxjh_X = -302f;
		x_Toa_Do_Class356.Rxjh_Y = -312f;
		x_Toa_Do_Class356.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class356);
		X_Toa_Do_Class x_Toa_Do_Class357 = new();
		x_Toa_Do_Class357.Rxjh_Map = 40101;
		x_Toa_Do_Class357.Rxjh_X = -302f;
		x_Toa_Do_Class357.Rxjh_Y = -321f;
		x_Toa_Do_Class357.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class357);
		X_Toa_Do_Class x_Toa_Do_Class358 = new();
		x_Toa_Do_Class358.Rxjh_Map = 40101;
		x_Toa_Do_Class358.Rxjh_X = -302f;
		x_Toa_Do_Class358.Rxjh_Y = -320f;
		x_Toa_Do_Class358.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class358);
		X_Toa_Do_Class x_Toa_Do_Class359 = new();
		x_Toa_Do_Class359.Rxjh_Map = 40101;
		x_Toa_Do_Class359.Rxjh_X = -302f;
		x_Toa_Do_Class359.Rxjh_Y = -329f;
		x_Toa_Do_Class359.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class359);
		X_Toa_Do_Class x_Toa_Do_Class360 = new();
		x_Toa_Do_Class360.Rxjh_Map = 40101;
		x_Toa_Do_Class360.Rxjh_X = -302f;
		x_Toa_Do_Class360.Rxjh_Y = -338f;
		x_Toa_Do_Class360.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class360);
		X_Toa_Do_Class x_Toa_Do_Class361 = new();
		x_Toa_Do_Class361.Rxjh_Map = 40101;
		x_Toa_Do_Class361.Rxjh_X = -302f;
		x_Toa_Do_Class361.Rxjh_Y = -347f;
		x_Toa_Do_Class361.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class361);
		X_Toa_Do_Class x_Toa_Do_Class362 = new();
		x_Toa_Do_Class362.Rxjh_Map = 40101;
		x_Toa_Do_Class362.Rxjh_X = -302f;
		x_Toa_Do_Class362.Rxjh_Y = -356f;
		x_Toa_Do_Class362.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class362);
		X_Toa_Do_Class x_Toa_Do_Class363 = new();
		x_Toa_Do_Class363.Rxjh_Map = 40101;
		x_Toa_Do_Class363.Rxjh_X = -302f;
		x_Toa_Do_Class363.Rxjh_Y = -365f;
		x_Toa_Do_Class363.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class363);
		X_Toa_Do_Class x_Toa_Do_Class364 = new();
		x_Toa_Do_Class364.Rxjh_Map = 40101;
		x_Toa_Do_Class364.Rxjh_X = -277f;
		x_Toa_Do_Class364.Rxjh_Y = -280f;
		x_Toa_Do_Class364.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class364);
		X_Toa_Do_Class x_Toa_Do_Class365 = new();
		x_Toa_Do_Class365.Rxjh_Map = 40101;
		x_Toa_Do_Class365.Rxjh_X = -277f;
		x_Toa_Do_Class365.Rxjh_Y = -289f;
		x_Toa_Do_Class365.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class365);
		X_Toa_Do_Class x_Toa_Do_Class366 = new();
		x_Toa_Do_Class366.Rxjh_Map = 40101;
		x_Toa_Do_Class366.Rxjh_X = -277f;
		x_Toa_Do_Class366.Rxjh_Y = -298f;
		x_Toa_Do_Class366.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class366);
		X_Toa_Do_Class x_Toa_Do_Class367 = new();
		x_Toa_Do_Class367.Rxjh_Map = 40101;
		x_Toa_Do_Class367.Rxjh_X = -277f;
		x_Toa_Do_Class367.Rxjh_Y = -307f;
		x_Toa_Do_Class367.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class367);
		X_Toa_Do_Class x_Toa_Do_Class368 = new();
		x_Toa_Do_Class368.Rxjh_Map = 40101;
		x_Toa_Do_Class368.Rxjh_X = -277f;
		x_Toa_Do_Class368.Rxjh_Y = -306f;
		x_Toa_Do_Class368.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class368);
		X_Toa_Do_Class x_Toa_Do_Class369 = new();
		x_Toa_Do_Class369.Rxjh_Map = 40101;
		x_Toa_Do_Class369.Rxjh_X = -277f;
		x_Toa_Do_Class369.Rxjh_Y = -315f;
		x_Toa_Do_Class369.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class369);
		X_Toa_Do_Class x_Toa_Do_Class370 = new();
		x_Toa_Do_Class370.Rxjh_Map = 40101;
		x_Toa_Do_Class370.Rxjh_X = -277f;
		x_Toa_Do_Class370.Rxjh_Y = -324f;
		x_Toa_Do_Class370.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class370);
		X_Toa_Do_Class x_Toa_Do_Class371 = new();
		x_Toa_Do_Class371.Rxjh_Map = 40101;
		x_Toa_Do_Class371.Rxjh_X = -277f;
		x_Toa_Do_Class371.Rxjh_Y = -335f;
		x_Toa_Do_Class371.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class371);
		X_Toa_Do_Class x_Toa_Do_Class372 = new();
		x_Toa_Do_Class372.Rxjh_Map = 40101;
		x_Toa_Do_Class372.Rxjh_X = -277f;
		x_Toa_Do_Class372.Rxjh_Y = -344f;
		x_Toa_Do_Class372.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class372);
		X_Toa_Do_Class x_Toa_Do_Class373 = new();
		x_Toa_Do_Class373.Rxjh_Map = 40101;
		x_Toa_Do_Class373.Rxjh_X = -277f;
		x_Toa_Do_Class373.Rxjh_Y = -353f;
		x_Toa_Do_Class373.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class373);
		X_Toa_Do_Class x_Toa_Do_Class374 = new();
		x_Toa_Do_Class374.Rxjh_Map = 40101;
		x_Toa_Do_Class374.Rxjh_X = -277f;
		x_Toa_Do_Class374.Rxjh_Y = -363f;
		x_Toa_Do_Class374.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class374);
		X_Toa_Do_Class x_Toa_Do_Class375 = new();
		x_Toa_Do_Class375.Rxjh_Map = 40101;
		x_Toa_Do_Class375.Rxjh_X = -290f;
		x_Toa_Do_Class375.Rxjh_Y = -285f;
		x_Toa_Do_Class375.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class375);
		X_Toa_Do_Class x_Toa_Do_Class376 = new();
		x_Toa_Do_Class376.Rxjh_Map = 40101;
		x_Toa_Do_Class376.Rxjh_X = -290f;
		x_Toa_Do_Class376.Rxjh_Y = -294f;
		x_Toa_Do_Class376.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class376);
		X_Toa_Do_Class x_Toa_Do_Class377 = new();
		x_Toa_Do_Class377.Rxjh_Map = 40101;
		x_Toa_Do_Class377.Rxjh_X = -290f;
		x_Toa_Do_Class377.Rxjh_Y = -303f;
		x_Toa_Do_Class377.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class377);
		X_Toa_Do_Class x_Toa_Do_Class378 = new();
		x_Toa_Do_Class378.Rxjh_Map = 40101;
		x_Toa_Do_Class378.Rxjh_X = -290f;
		x_Toa_Do_Class378.Rxjh_Y = -312f;
		x_Toa_Do_Class378.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class378);
		X_Toa_Do_Class x_Toa_Do_Class379 = new();
		x_Toa_Do_Class379.Rxjh_Map = 40101;
		x_Toa_Do_Class379.Rxjh_X = -290f;
		x_Toa_Do_Class379.Rxjh_Y = -321f;
		x_Toa_Do_Class379.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class379);
		X_Toa_Do_Class x_Toa_Do_Class380 = new();
		x_Toa_Do_Class380.Rxjh_Map = 40101;
		x_Toa_Do_Class380.Rxjh_X = -290f;
		x_Toa_Do_Class380.Rxjh_Y = -330f;
		x_Toa_Do_Class380.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class380);
		X_Toa_Do_Class x_Toa_Do_Class381 = new();
		x_Toa_Do_Class381.Rxjh_Map = 40101;
		x_Toa_Do_Class381.Rxjh_X = -290f;
		x_Toa_Do_Class381.Rxjh_Y = -339f;
		x_Toa_Do_Class381.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class381);
		X_Toa_Do_Class x_Toa_Do_Class382 = new();
		x_Toa_Do_Class382.Rxjh_Map = 40101;
		x_Toa_Do_Class382.Rxjh_X = -290f;
		x_Toa_Do_Class382.Rxjh_Y = -348f;
		x_Toa_Do_Class382.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class382);
		X_Toa_Do_Class x_Toa_Do_Class383 = new();
		x_Toa_Do_Class383.Rxjh_Map = 40101;
		x_Toa_Do_Class383.Rxjh_X = -290f;
		x_Toa_Do_Class383.Rxjh_Y = -357f;
		x_Toa_Do_Class383.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class383);
		X_Toa_Do_Class x_Toa_Do_Class384 = new();
		x_Toa_Do_Class384.Rxjh_Map = 40101;
		x_Toa_Do_Class384.Rxjh_X = -290f;
		x_Toa_Do_Class384.Rxjh_Y = -362f;
		x_Toa_Do_Class384.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class384);
		X_Toa_Do_Class x_Toa_Do_Class385 = new();
		x_Toa_Do_Class385.Rxjh_Map = 40101;
		x_Toa_Do_Class385.Rxjh_X = -205f;
		x_Toa_Do_Class385.Rxjh_Y = -804f;
		x_Toa_Do_Class385.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class385);
		X_Toa_Do_Class x_Toa_Do_Class386 = new();
		x_Toa_Do_Class386.Rxjh_Map = 40101;
		x_Toa_Do_Class386.Rxjh_X = -194f;
		x_Toa_Do_Class386.Rxjh_Y = -804f;
		x_Toa_Do_Class386.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class386);
		X_Toa_Do_Class x_Toa_Do_Class387 = new();
		x_Toa_Do_Class387.Rxjh_Map = 40101;
		x_Toa_Do_Class387.Rxjh_X = -183f;
		x_Toa_Do_Class387.Rxjh_Y = -804f;
		x_Toa_Do_Class387.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class387);
		X_Toa_Do_Class x_Toa_Do_Class388 = new();
		x_Toa_Do_Class388.Rxjh_Map = 40101;
		x_Toa_Do_Class388.Rxjh_X = -172f;
		x_Toa_Do_Class388.Rxjh_Y = -804f;
		x_Toa_Do_Class388.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class388);
		X_Toa_Do_Class x_Toa_Do_Class389 = new();
		x_Toa_Do_Class389.Rxjh_Map = 40101;
		x_Toa_Do_Class389.Rxjh_X = -161f;
		x_Toa_Do_Class389.Rxjh_Y = -804f;
		x_Toa_Do_Class389.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class389);
		X_Toa_Do_Class x_Toa_Do_Class390 = new();
		x_Toa_Do_Class390.Rxjh_Map = 40101;
		x_Toa_Do_Class390.Rxjh_X = -150f;
		x_Toa_Do_Class390.Rxjh_Y = -804f;
		x_Toa_Do_Class390.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class390);
		X_Toa_Do_Class x_Toa_Do_Class391 = new();
		x_Toa_Do_Class391.Rxjh_Map = 40101;
		x_Toa_Do_Class391.Rxjh_X = -141f;
		x_Toa_Do_Class391.Rxjh_Y = -804f;
		x_Toa_Do_Class391.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class391);
		X_Toa_Do_Class x_Toa_Do_Class392 = new();
		x_Toa_Do_Class392.Rxjh_Map = 40101;
		x_Toa_Do_Class392.Rxjh_X = -132f;
		x_Toa_Do_Class392.Rxjh_Y = -804f;
		x_Toa_Do_Class392.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class392);
		X_Toa_Do_Class x_Toa_Do_Class393 = new();
		x_Toa_Do_Class393.Rxjh_Map = 40101;
		x_Toa_Do_Class393.Rxjh_X = -123f;
		x_Toa_Do_Class393.Rxjh_Y = -804f;
		x_Toa_Do_Class393.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class393);
		X_Toa_Do_Class x_Toa_Do_Class394 = new();
		x_Toa_Do_Class394.Rxjh_Map = 40101;
		x_Toa_Do_Class394.Rxjh_X = -119f;
		x_Toa_Do_Class394.Rxjh_Y = -804f;
		x_Toa_Do_Class394.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class394);
		X_Toa_Do_Class x_Toa_Do_Class395 = new();
		x_Toa_Do_Class395.Rxjh_Map = 40101;
		x_Toa_Do_Class395.Rxjh_X = -200f;
		x_Toa_Do_Class395.Rxjh_Y = -786f;
		x_Toa_Do_Class395.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class395);
		X_Toa_Do_Class x_Toa_Do_Class396 = new();
		x_Toa_Do_Class396.Rxjh_Map = 40101;
		x_Toa_Do_Class396.Rxjh_X = -191f;
		x_Toa_Do_Class396.Rxjh_Y = -786f;
		x_Toa_Do_Class396.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class396);
		X_Toa_Do_Class x_Toa_Do_Class397 = new();
		x_Toa_Do_Class397.Rxjh_Map = 40101;
		x_Toa_Do_Class397.Rxjh_X = -182f;
		x_Toa_Do_Class397.Rxjh_Y = -786f;
		x_Toa_Do_Class397.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class397);
		X_Toa_Do_Class x_Toa_Do_Class398 = new();
		x_Toa_Do_Class398.Rxjh_Map = 40101;
		x_Toa_Do_Class398.Rxjh_X = -173f;
		x_Toa_Do_Class398.Rxjh_Y = -786f;
		x_Toa_Do_Class398.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class398);
		X_Toa_Do_Class x_Toa_Do_Class399 = new();
		x_Toa_Do_Class399.Rxjh_Map = 40101;
		x_Toa_Do_Class399.Rxjh_X = -164f;
		x_Toa_Do_Class399.Rxjh_Y = -786f;
		x_Toa_Do_Class399.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class399);
		X_Toa_Do_Class x_Toa_Do_Class400 = new();
		x_Toa_Do_Class400.Rxjh_Map = 40101;
		x_Toa_Do_Class400.Rxjh_X = -155f;
		x_Toa_Do_Class400.Rxjh_Y = -786f;
		x_Toa_Do_Class400.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class400);
		X_Toa_Do_Class x_Toa_Do_Class401 = new();
		x_Toa_Do_Class401.Rxjh_Map = 40101;
		x_Toa_Do_Class401.Rxjh_X = -146f;
		x_Toa_Do_Class401.Rxjh_Y = -786f;
		x_Toa_Do_Class401.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class401);
		X_Toa_Do_Class x_Toa_Do_Class402 = new();
		x_Toa_Do_Class402.Rxjh_Map = 40101;
		x_Toa_Do_Class402.Rxjh_X = -137f;
		x_Toa_Do_Class402.Rxjh_Y = -786f;
		x_Toa_Do_Class402.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class402);
		X_Toa_Do_Class x_Toa_Do_Class403 = new();
		x_Toa_Do_Class403.Rxjh_Map = 40101;
		x_Toa_Do_Class403.Rxjh_X = -128f;
		x_Toa_Do_Class403.Rxjh_Y = -786f;
		x_Toa_Do_Class403.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class403);
		X_Toa_Do_Class x_Toa_Do_Class404 = new();
		x_Toa_Do_Class404.Rxjh_Map = 40101;
		x_Toa_Do_Class404.Rxjh_X = -119f;
		x_Toa_Do_Class404.Rxjh_Y = -786f;
		x_Toa_Do_Class404.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class404);
		X_Toa_Do_Class x_Toa_Do_Class405 = new();
		x_Toa_Do_Class405.Rxjh_Map = 40101;
		x_Toa_Do_Class405.Rxjh_X = -112f;
		x_Toa_Do_Class405.Rxjh_Y = -786f;
		x_Toa_Do_Class405.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class405);
		X_Toa_Do_Class x_Toa_Do_Class406 = new();
		x_Toa_Do_Class406.Rxjh_Map = 40101;
		x_Toa_Do_Class406.Rxjh_X = -202f;
		x_Toa_Do_Class406.Rxjh_Y = -796f;
		x_Toa_Do_Class406.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class406);
		X_Toa_Do_Class x_Toa_Do_Class407 = new();
		x_Toa_Do_Class407.Rxjh_Map = 40101;
		x_Toa_Do_Class407.Rxjh_X = -193f;
		x_Toa_Do_Class407.Rxjh_Y = -796f;
		x_Toa_Do_Class407.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class407);
		X_Toa_Do_Class x_Toa_Do_Class408 = new();
		x_Toa_Do_Class408.Rxjh_Map = 40101;
		x_Toa_Do_Class408.Rxjh_X = -184f;
		x_Toa_Do_Class408.Rxjh_Y = -796f;
		x_Toa_Do_Class408.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class408);
		X_Toa_Do_Class x_Toa_Do_Class409 = new();
		x_Toa_Do_Class409.Rxjh_Map = 40101;
		x_Toa_Do_Class409.Rxjh_X = -175f;
		x_Toa_Do_Class409.Rxjh_Y = -796f;
		x_Toa_Do_Class409.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class409);
		X_Toa_Do_Class x_Toa_Do_Class410 = new();
		x_Toa_Do_Class410.Rxjh_Map = 40101;
		x_Toa_Do_Class410.Rxjh_X = -166f;
		x_Toa_Do_Class410.Rxjh_Y = -796f;
		x_Toa_Do_Class410.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class410);
		X_Toa_Do_Class x_Toa_Do_Class411 = new();
		x_Toa_Do_Class411.Rxjh_Map = 40101;
		x_Toa_Do_Class411.Rxjh_X = -157f;
		x_Toa_Do_Class411.Rxjh_Y = -796f;
		x_Toa_Do_Class411.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class411);
		X_Toa_Do_Class x_Toa_Do_Class412 = new();
		x_Toa_Do_Class412.Rxjh_Map = 40101;
		x_Toa_Do_Class412.Rxjh_X = -148f;
		x_Toa_Do_Class412.Rxjh_Y = -796f;
		x_Toa_Do_Class412.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class412);
		X_Toa_Do_Class x_Toa_Do_Class413 = new();
		x_Toa_Do_Class413.Rxjh_Map = 40101;
		x_Toa_Do_Class413.Rxjh_X = -139f;
		x_Toa_Do_Class413.Rxjh_Y = -796f;
		x_Toa_Do_Class413.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class413);
		X_Toa_Do_Class x_Toa_Do_Class414 = new();
		x_Toa_Do_Class414.Rxjh_Map = 40101;
		x_Toa_Do_Class414.Rxjh_X = -130f;
		x_Toa_Do_Class414.Rxjh_Y = -796f;
		x_Toa_Do_Class414.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class414);
		X_Toa_Do_Class x_Toa_Do_Class415 = new();
		x_Toa_Do_Class415.Rxjh_Map = 40101;
		x_Toa_Do_Class415.Rxjh_X = -121f;
		x_Toa_Do_Class415.Rxjh_Y = -796f;
		x_Toa_Do_Class415.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class415);
		X_Toa_Do_Class x_Toa_Do_Class416 = new();
		x_Toa_Do_Class416.Rxjh_Map = 40101;
		x_Toa_Do_Class416.Rxjh_X = -112f;
		x_Toa_Do_Class416.Rxjh_Y = -796f;
		x_Toa_Do_Class416.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class416);
		X_Toa_Do_Class x_Toa_Do_Class417 = new();
		x_Toa_Do_Class417.Rxjh_Map = 40101;
		x_Toa_Do_Class417.Rxjh_X = -103f;
		x_Toa_Do_Class417.Rxjh_Y = -796f;
		x_Toa_Do_Class417.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class417);
		X_Toa_Do_Class x_Toa_Do_Class418 = new();
		x_Toa_Do_Class418.Rxjh_Map = 40101;
		x_Toa_Do_Class418.Rxjh_X = -8f;
		x_Toa_Do_Class418.Rxjh_Y = -796f;
		x_Toa_Do_Class418.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class418);
		X_Toa_Do_Class x_Toa_Do_Class419 = new();
		x_Toa_Do_Class35.Rxjh_Map = 40101;
		x_Toa_Do_Class35.Rxjh_X = -150f;
		x_Toa_Do_Class35.Rxjh_Y = -815f;
		x_Toa_Do_Class35.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class35);
		X_Toa_Do_Class x_Toa_Do_Class420 = new();
		x_Toa_Do_Class420.Rxjh_Map = 40101;
		x_Toa_Do_Class420.Rxjh_X = -165f;
		x_Toa_Do_Class420.Rxjh_Y = -811f;
		x_Toa_Do_Class420.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class420);
		X_Toa_Do_Class x_Toa_Do_Class421 = new();
		x_Toa_Do_Class421.Rxjh_Map = 40101;
		x_Toa_Do_Class421.Rxjh_X = -176f;
		x_Toa_Do_Class421.Rxjh_Y = -813f;
		x_Toa_Do_Class421.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class421);
		X_Toa_Do_Class x_Toa_Do_Class422 = new();
		x_Toa_Do_Class422.Rxjh_Map = 40101;
		x_Toa_Do_Class422.Rxjh_X = 96f;
		x_Toa_Do_Class422.Rxjh_Y = -820f;
		x_Toa_Do_Class422.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class422);
		X_Toa_Do_Class x_Toa_Do_Class423 = new();
		x_Toa_Do_Class423.Rxjh_Map = 40101;
		x_Toa_Do_Class423.Rxjh_X = 105f;
		x_Toa_Do_Class423.Rxjh_Y = -814f;
		x_Toa_Do_Class423.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class423);
		X_Toa_Do_Class x_Toa_Do_Class424 = new();
		x_Toa_Do_Class424.Rxjh_Map = 40101;
		x_Toa_Do_Class424.Rxjh_X = 115f;
		x_Toa_Do_Class424.Rxjh_Y = -812f;
		x_Toa_Do_Class424.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class424);
		X_Toa_Do_Class x_Toa_Do_Class425 = new();
		x_Toa_Do_Class425.Rxjh_Map = 40101;
		x_Toa_Do_Class425.Rxjh_X = 125f;
		x_Toa_Do_Class425.Rxjh_Y = -812f;
		x_Toa_Do_Class425.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class425);
		X_Toa_Do_Class x_Toa_Do_Class426 = new();
		x_Toa_Do_Class426.Rxjh_Map = 40101;
		x_Toa_Do_Class426.Rxjh_X = 132f;
		x_Toa_Do_Class426.Rxjh_Y = -819f;
		x_Toa_Do_Class426.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class426);
		X_Toa_Do_Class x_Toa_Do_Class427 = new();
		x_Toa_Do_Class427.Rxjh_Map = 40101;
		x_Toa_Do_Class427.Rxjh_X = 140f;
		x_Toa_Do_Class427.Rxjh_Y = -812f;
		x_Toa_Do_Class427.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class427);
		X_Toa_Do_Class x_Toa_Do_Class428 = new();
		x_Toa_Do_Class428.Rxjh_Map = 40101;
		x_Toa_Do_Class428.Rxjh_X = 149f;
		x_Toa_Do_Class428.Rxjh_Y = -812f;
		x_Toa_Do_Class428.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class428);
		X_Toa_Do_Class x_Toa_Do_Class429 = new();
		x_Toa_Do_Class429.Rxjh_Map = 40101;
		x_Toa_Do_Class429.Rxjh_X = 162f;
		x_Toa_Do_Class429.Rxjh_Y = -819f;
		x_Toa_Do_Class429.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class429);
		X_Toa_Do_Class x_Toa_Do_Class430 = new();
		x_Toa_Do_Class430.Rxjh_Map = 40101;
		x_Toa_Do_Class430.Rxjh_X = 172f;
		x_Toa_Do_Class430.Rxjh_Y = -815f;
		x_Toa_Do_Class430.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class430);
		X_Toa_Do_Class x_Toa_Do_Class431 = new();
		x_Toa_Do_Class431.Rxjh_Map = 40101;
		x_Toa_Do_Class431.Rxjh_X = 180f;
		x_Toa_Do_Class431.Rxjh_Y = -818f;
		x_Toa_Do_Class431.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class431);
		X_Toa_Do_Class x_Toa_Do_Class432 = new();
		x_Toa_Do_Class432.Rxjh_Map = 40101;
		x_Toa_Do_Class432.Rxjh_X = 180f;
		x_Toa_Do_Class432.Rxjh_Y = -796f;
		x_Toa_Do_Class432.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class432);
		X_Toa_Do_Class x_Toa_Do_Class433 = new();
		x_Toa_Do_Class433.Rxjh_Map = 40101;
		x_Toa_Do_Class433.Rxjh_X = 171f;
		x_Toa_Do_Class433.Rxjh_Y = -796f;
		x_Toa_Do_Class433.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class433);
		X_Toa_Do_Class x_Toa_Do_Class434 = new();
		x_Toa_Do_Class434.Rxjh_Map = 40101;
		x_Toa_Do_Class434.Rxjh_X = 162f;
		x_Toa_Do_Class434.Rxjh_Y = -796f;
		x_Toa_Do_Class434.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class434);
		X_Toa_Do_Class x_Toa_Do_Class435 = new();
		x_Toa_Do_Class435.Rxjh_Map = 40101;
		x_Toa_Do_Class435.Rxjh_X = 153f;
		x_Toa_Do_Class435.Rxjh_Y = -796f;
		x_Toa_Do_Class435.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class435);
		X_Toa_Do_Class x_Toa_Do_Class436 = new();
		x_Toa_Do_Class436.Rxjh_Map = 40101;
		x_Toa_Do_Class436.Rxjh_X = 144f;
		x_Toa_Do_Class436.Rxjh_Y = -796f;
		x_Toa_Do_Class436.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class436);
		X_Toa_Do_Class x_Toa_Do_Class437 = new();
		x_Toa_Do_Class437.Rxjh_Map = 40101;
		x_Toa_Do_Class437.Rxjh_X = 135f;
		x_Toa_Do_Class437.Rxjh_Y = -796f;
		x_Toa_Do_Class437.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class437);
		X_Toa_Do_Class x_Toa_Do_Class438 = new();
		x_Toa_Do_Class438.Rxjh_Map = 40101;
		x_Toa_Do_Class438.Rxjh_X = 126f;
		x_Toa_Do_Class438.Rxjh_Y = -796f;
		x_Toa_Do_Class438.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class438);
		X_Toa_Do_Class x_Toa_Do_Class439 = new();
		x_Toa_Do_Class439.Rxjh_Map = 40101;
		x_Toa_Do_Class439.Rxjh_X = 117f;
		x_Toa_Do_Class439.Rxjh_Y = -796f;
		x_Toa_Do_Class439.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class439);
		X_Toa_Do_Class x_Toa_Do_Class440 = new();
		x_Toa_Do_Class440.Rxjh_Map = 40101;
		x_Toa_Do_Class440.Rxjh_X = -108f;
		x_Toa_Do_Class440.Rxjh_Y = -796f;
		x_Toa_Do_Class440.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class440);
		X_Toa_Do_Class x_Toa_Do_Class441 = new();
		x_Toa_Do_Class441.Rxjh_Map = 40101;
		x_Toa_Do_Class441.Rxjh_X = 99f;
		x_Toa_Do_Class441.Rxjh_Y = -796f;
		x_Toa_Do_Class441.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class441);
		X_Toa_Do_Class x_Toa_Do_Class442 = new();
		x_Toa_Do_Class442.Rxjh_Map = 40101;
		x_Toa_Do_Class442.Rxjh_X = 337f;
		x_Toa_Do_Class442.Rxjh_Y = -366f;
		x_Toa_Do_Class442.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class442);
		X_Toa_Do_Class x_Toa_Do_Class443 = new();
		x_Toa_Do_Class443.Rxjh_Map = 40101;
		x_Toa_Do_Class443.Rxjh_X = 337f;
		x_Toa_Do_Class443.Rxjh_Y = -358f;
		x_Toa_Do_Class443.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class443);
		X_Toa_Do_Class x_Toa_Do_Class444 = new();
		x_Toa_Do_Class444.Rxjh_Map = 40101;
		x_Toa_Do_Class444.Rxjh_X = 337f;
		x_Toa_Do_Class444.Rxjh_Y = -349f;
		x_Toa_Do_Class444.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class444);
		X_Toa_Do_Class x_Toa_Do_Class445 = new();
		x_Toa_Do_Class445.Rxjh_Map = 40101;
		x_Toa_Do_Class445.Rxjh_X = 337f;
		x_Toa_Do_Class445.Rxjh_Y = -340f;
		x_Toa_Do_Class445.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class445);
		X_Toa_Do_Class x_Toa_Do_Class446 = new();
		x_Toa_Do_Class446.Rxjh_Map = 40101;
		x_Toa_Do_Class446.Rxjh_X = 337f;
		x_Toa_Do_Class446.Rxjh_Y = -331f;
		x_Toa_Do_Class446.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class446);
		X_Toa_Do_Class x_Toa_Do_Class447 = new();
		x_Toa_Do_Class447.Rxjh_Map = 40101;
		x_Toa_Do_Class447.Rxjh_X = 337f;
		x_Toa_Do_Class447.Rxjh_Y = -322f;
		x_Toa_Do_Class447.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class447);
		X_Toa_Do_Class x_Toa_Do_Class448 = new();
		x_Toa_Do_Class448.Rxjh_Map = 40101;
		x_Toa_Do_Class448.Rxjh_X = 337f;
		x_Toa_Do_Class448.Rxjh_Y = -313f;
		x_Toa_Do_Class448.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class448);
		X_Toa_Do_Class x_Toa_Do_Class449 = new();
		x_Toa_Do_Class449.Rxjh_Map = 40101;
		x_Toa_Do_Class449.Rxjh_X = 337f;
		x_Toa_Do_Class449.Rxjh_Y = -304f;
		x_Toa_Do_Class449.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class449);
		X_Toa_Do_Class x_Toa_Do_Class450 = new();
		x_Toa_Do_Class450.Rxjh_Map = 40101;
		x_Toa_Do_Class450.Rxjh_X = 337f;
		x_Toa_Do_Class450.Rxjh_Y = -295f;
		x_Toa_Do_Class450.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class450);
		X_Toa_Do_Class x_Toa_Do_Class451 = new();
		x_Toa_Do_Class451.Rxjh_Map = 40101;
		x_Toa_Do_Class451.Rxjh_X = 337f;
		x_Toa_Do_Class451.Rxjh_Y = -286f;
		x_Toa_Do_Class451.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class451);
		X_Toa_Do_Class x_Toa_Do_Class452 = new();
		x_Toa_Do_Class452.Rxjh_Map = 40101;
		x_Toa_Do_Class452.Rxjh_X = 337f;
		x_Toa_Do_Class452.Rxjh_Y = -277f;
		x_Toa_Do_Class452.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class452);
		X_Toa_Do_Class x_Toa_Do_Class453 = new();
		x_Toa_Do_Class453.Rxjh_Map = 40101;
		x_Toa_Do_Class453.Rxjh_X = 337f;
		x_Toa_Do_Class453.Rxjh_Y = -268f;
		x_Toa_Do_Class453.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class453);
		X_Toa_Do_Class x_Toa_Do_Class454 = new();
		x_Toa_Do_Class454.Rxjh_Map = 40101;
		x_Toa_Do_Class454.Rxjh_X = 337f;
		x_Toa_Do_Class454.Rxjh_Y = -262f;
		x_Toa_Do_Class454.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class454);
		X_Toa_Do_Class x_Toa_Do_Class455 = new();
		x_Toa_Do_Class455.Rxjh_Map = 40101;
		x_Toa_Do_Class455.Rxjh_X = 317f;
		x_Toa_Do_Class455.Rxjh_Y = -370f;
		x_Toa_Do_Class455.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class455);
		X_Toa_Do_Class x_Toa_Do_Class456 = new();
		x_Toa_Do_Class456.Rxjh_Map = 40101;
		x_Toa_Do_Class456.Rxjh_X = 311f;
		x_Toa_Do_Class456.Rxjh_Y = -361f;
		x_Toa_Do_Class456.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class456);
		X_Toa_Do_Class x_Toa_Do_Class457 = new();
		x_Toa_Do_Class457.Rxjh_Map = 40101;
		x_Toa_Do_Class457.Rxjh_X = 317f;
		x_Toa_Do_Class457.Rxjh_Y = -347f;
		x_Toa_Do_Class457.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class457);
		X_Toa_Do_Class x_Toa_Do_Class458 = new();
		x_Toa_Do_Class458.Rxjh_Map = 40101;
		x_Toa_Do_Class458.Rxjh_X = 310f;
		x_Toa_Do_Class458.Rxjh_Y = -332f;
		x_Toa_Do_Class458.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class458);
		X_Toa_Do_Class x_Toa_Do_Class459 = new();
		x_Toa_Do_Class459.Rxjh_Map = 40101;
		x_Toa_Do_Class459.Rxjh_X = 316f;
		x_Toa_Do_Class459.Rxjh_Y = -318f;
		x_Toa_Do_Class459.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class459);
		X_Toa_Do_Class x_Toa_Do_Class460 = new();
		x_Toa_Do_Class460.Rxjh_Map = 40101;
		x_Toa_Do_Class460.Rxjh_X = 319f;
		x_Toa_Do_Class460.Rxjh_Y = -307f;
		x_Toa_Do_Class460.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class460);
		X_Toa_Do_Class x_Toa_Do_Class461 = new();
		x_Toa_Do_Class461.Rxjh_Map = 40101;
		x_Toa_Do_Class461.Rxjh_X = 319f;
		x_Toa_Do_Class461.Rxjh_Y = -294f;
		x_Toa_Do_Class461.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class461);
		X_Toa_Do_Class x_Toa_Do_Class462 = new();
		x_Toa_Do_Class462.Rxjh_Map = 40101;
		x_Toa_Do_Class462.Rxjh_X = 315f;
		x_Toa_Do_Class462.Rxjh_Y = -281f;
		x_Toa_Do_Class462.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class462);
		X_Toa_Do_Class x_Toa_Do_Class463 = new();
		x_Toa_Do_Class463.Rxjh_Map = 40101;
		x_Toa_Do_Class463.Rxjh_X = 321f;
		x_Toa_Do_Class463.Rxjh_Y = -266f;
		x_Toa_Do_Class463.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class463);
		X_Toa_Do_Class x_Toa_Do_Class464 = new();
		x_Toa_Do_Class464.Rxjh_Map = 40101;
		x_Toa_Do_Class464.Rxjh_X = 326f;
		x_Toa_Do_Class464.Rxjh_Y = -359f;
		x_Toa_Do_Class464.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class464);
		X_Toa_Do_Class x_Toa_Do_Class465 = new();
		x_Toa_Do_Class465.Rxjh_Map = 40101;
		x_Toa_Do_Class465.Rxjh_X = 308f;
		x_Toa_Do_Class465.Rxjh_Y = -603f;
		x_Toa_Do_Class465.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class465);
		X_Toa_Do_Class x_Toa_Do_Class466 = new();
		x_Toa_Do_Class466.Rxjh_Map = 40101;
		x_Toa_Do_Class466.Rxjh_X = 306f;
		x_Toa_Do_Class466.Rxjh_Y = -616f;
		x_Toa_Do_Class466.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class466);
		X_Toa_Do_Class x_Toa_Do_Class467 = new();
		x_Toa_Do_Class467.Rxjh_Map = 40101;
		x_Toa_Do_Class467.Rxjh_X = 307f;
		x_Toa_Do_Class467.Rxjh_Y = -631f;
		x_Toa_Do_Class467.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class467);
		X_Toa_Do_Class x_Toa_Do_Class468 = new();
		x_Toa_Do_Class468.Rxjh_Map = 40101;
		x_Toa_Do_Class468.Rxjh_X = 305f;
		x_Toa_Do_Class468.Rxjh_Y = -647f;
		x_Toa_Do_Class468.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class468);
		X_Toa_Do_Class x_Toa_Do_Class469 = new();
		x_Toa_Do_Class469.Rxjh_Map = 40101;
		x_Toa_Do_Class469.Rxjh_X = 303f;
		x_Toa_Do_Class469.Rxjh_Y = -660f;
		x_Toa_Do_Class469.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class469);
		X_Toa_Do_Class x_Toa_Do_Class470 = new();
		x_Toa_Do_Class470.Rxjh_Map = 40101;
		x_Toa_Do_Class470.Rxjh_X = 309f;
		x_Toa_Do_Class470.Rxjh_Y = -675f;
		x_Toa_Do_Class470.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class470);
		X_Toa_Do_Class x_Toa_Do_Class471 = new();
		x_Toa_Do_Class471.Rxjh_Map = 40101;
		x_Toa_Do_Class471.Rxjh_X = 310f;
		x_Toa_Do_Class471.Rxjh_Y = -687f;
		x_Toa_Do_Class471.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class471);
		X_Toa_Do_Class x_Toa_Do_Class472 = new();
		x_Toa_Do_Class472.Rxjh_Map = 40101;
		x_Toa_Do_Class472.Rxjh_X = 310f;
		x_Toa_Do_Class472.Rxjh_Y = -699f;
		x_Toa_Do_Class472.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class472);
		X_Toa_Do_Class x_Toa_Do_Class473 = new();
		x_Toa_Do_Class473.Rxjh_Map = 40101;
		x_Toa_Do_Class473.Rxjh_X = 333f;
		x_Toa_Do_Class473.Rxjh_Y = -695f;
		x_Toa_Do_Class473.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class473);
		X_Toa_Do_Class x_Toa_Do_Class474 = new();
		x_Toa_Do_Class474.Rxjh_Map = 40101;
		x_Toa_Do_Class474.Rxjh_X = 324f;
		x_Toa_Do_Class474.Rxjh_Y = -685f;
		x_Toa_Do_Class474.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class474);
		X_Toa_Do_Class x_Toa_Do_Class475 = new();
		x_Toa_Do_Class475.Rxjh_Map = 40101;
		x_Toa_Do_Class475.Rxjh_X = -312f;
		x_Toa_Do_Class475.Rxjh_Y = 368f;
		x_Toa_Do_Class475.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class475);
		X_Toa_Do_Class x_Toa_Do_Class476 = new();
		x_Toa_Do_Class476.Rxjh_Map = 40101;
		x_Toa_Do_Class476.Rxjh_X = -312f;
		x_Toa_Do_Class476.Rxjh_Y = 359f;
		x_Toa_Do_Class476.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class476);
		X_Toa_Do_Class x_Toa_Do_Class477 = new();
		x_Toa_Do_Class477.Rxjh_Map = 40101;
		x_Toa_Do_Class477.Rxjh_X = -312f;
		x_Toa_Do_Class477.Rxjh_Y = 350f;
		x_Toa_Do_Class477.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class477);
		X_Toa_Do_Class x_Toa_Do_Class478 = new();
		x_Toa_Do_Class478.Rxjh_Map = 40101;
		x_Toa_Do_Class478.Rxjh_X = -312f;
		x_Toa_Do_Class478.Rxjh_Y = 341f;
		x_Toa_Do_Class478.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class478);
		X_Toa_Do_Class x_Toa_Do_Class479 = new();
		x_Toa_Do_Class479.Rxjh_Map = 40101;
		x_Toa_Do_Class479.Rxjh_X = -312f;
		x_Toa_Do_Class479.Rxjh_Y = 332f;
		x_Toa_Do_Class479.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class479);
		X_Toa_Do_Class x_Toa_Do_Class480 = new();
		x_Toa_Do_Class480.Rxjh_Map = 40101;
		x_Toa_Do_Class480.Rxjh_X = -312f;
		x_Toa_Do_Class480.Rxjh_Y = 324f;
		x_Toa_Do_Class480.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class480);
		X_Toa_Do_Class x_Toa_Do_Class481 = new();
		x_Toa_Do_Class481.Rxjh_Map = 40101;
		x_Toa_Do_Class481.Rxjh_X = -311f;
		x_Toa_Do_Class481.Rxjh_Y = 315f;
		x_Toa_Do_Class481.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class481);
		X_Toa_Do_Class x_Toa_Do_Class482 = new();
		x_Toa_Do_Class482.Rxjh_Map = 40101;
		x_Toa_Do_Class482.Rxjh_X = -310f;
		x_Toa_Do_Class482.Rxjh_Y = 306f;
		x_Toa_Do_Class482.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class482);
		X_Toa_Do_Class x_Toa_Do_Class483 = new();
		x_Toa_Do_Class483.Rxjh_Map = 40101;
		x_Toa_Do_Class483.Rxjh_X = -304f;
		x_Toa_Do_Class483.Rxjh_Y = 297f;
		x_Toa_Do_Class483.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class483);
		X_Toa_Do_Class x_Toa_Do_Class484 = new();
		x_Toa_Do_Class484.Rxjh_Map = 40101;
		x_Toa_Do_Class484.Rxjh_X = 205f;
		x_Toa_Do_Class484.Rxjh_Y = 42f;
		x_Toa_Do_Class484.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class484);
		X_Toa_Do_Class x_Toa_Do_Class485 = new();
		x_Toa_Do_Class485.Rxjh_Map = 40101;
		x_Toa_Do_Class485.Rxjh_X = -300f;
		x_Toa_Do_Class485.Rxjh_Y = 287f;
		x_Toa_Do_Class485.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class485);
		X_Toa_Do_Class x_Toa_Do_Class486 = new();
		x_Toa_Do_Class486.Rxjh_Map = 40101;
		x_Toa_Do_Class486.Rxjh_X = 295f;
		x_Toa_Do_Class486.Rxjh_Y = 286f;
		x_Toa_Do_Class486.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class486);
		X_Toa_Do_Class x_Toa_Do_Class487 = new();
		x_Toa_Do_Class487.Rxjh_Map = 40101;
		x_Toa_Do_Class487.Rxjh_X = -294f;
		x_Toa_Do_Class487.Rxjh_Y = 313f;
		x_Toa_Do_Class487.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class487);
		X_Toa_Do_Class x_Toa_Do_Class488 = new();
		x_Toa_Do_Class488.Rxjh_Map = 40101;
		x_Toa_Do_Class488.Rxjh_X = -294f;
		x_Toa_Do_Class488.Rxjh_Y = 313f;
		x_Toa_Do_Class488.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class488);
		X_Toa_Do_Class x_Toa_Do_Class489 = new();
		x_Toa_Do_Class489.Rxjh_Map = 40101;
		x_Toa_Do_Class489.Rxjh_X = -297f;
		x_Toa_Do_Class489.Rxjh_Y = 326f;
		x_Toa_Do_Class489.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class489);
		X_Toa_Do_Class x_Toa_Do_Class490 = new();
		x_Toa_Do_Class490.Rxjh_Map = 40101;
		x_Toa_Do_Class490.Rxjh_X = -296f;
		x_Toa_Do_Class490.Rxjh_Y = 343f;
		x_Toa_Do_Class490.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class490);
		X_Toa_Do_Class x_Toa_Do_Class491 = new();
		x_Toa_Do_Class491.Rxjh_Map = 40101;
		x_Toa_Do_Class491.Rxjh_X = -297f;
		x_Toa_Do_Class491.Rxjh_Y = 361f;
		x_Toa_Do_Class491.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class491);
		X_Toa_Do_Class x_Toa_Do_Class492 = new();
		x_Toa_Do_Class492.Rxjh_Map = 40101;
		x_Toa_Do_Class492.Rxjh_X = -278f;
		x_Toa_Do_Class492.Rxjh_Y = 665f;
		x_Toa_Do_Class492.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class492);
		X_Toa_Do_Class x_Toa_Do_Class493 = new();
		x_Toa_Do_Class493.Rxjh_Map = 40101;
		x_Toa_Do_Class493.Rxjh_X = -290f;
		x_Toa_Do_Class493.Rxjh_Y = 656f;
		x_Toa_Do_Class493.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class493);
		X_Toa_Do_Class x_Toa_Do_Class494 = new();
		x_Toa_Do_Class494.Rxjh_Map = 40101;
		x_Toa_Do_Class494.Rxjh_X = -290f;
		x_Toa_Do_Class494.Rxjh_Y = 643f;
		x_Toa_Do_Class494.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class494);
		X_Toa_Do_Class x_Toa_Do_Class495 = new();
		x_Toa_Do_Class495.Rxjh_Map = 40101;
		x_Toa_Do_Class495.Rxjh_X = -289f;
		x_Toa_Do_Class495.Rxjh_Y = 626f;
		x_Toa_Do_Class495.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class495);
		X_Toa_Do_Class x_Toa_Do_Class496 = new();
		x_Toa_Do_Class496.Rxjh_Map = 40101;
		x_Toa_Do_Class496.Rxjh_X = -287f;
		x_Toa_Do_Class496.Rxjh_Y = 609f;
		x_Toa_Do_Class496.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class496);
		X_Toa_Do_Class x_Toa_Do_Class497 = new();
		x_Toa_Do_Class497.Rxjh_Map = 40101;
		x_Toa_Do_Class497.Rxjh_X = -288f;
		x_Toa_Do_Class497.Rxjh_Y = 596f;
		x_Toa_Do_Class497.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class497);
		X_Toa_Do_Class x_Toa_Do_Class498 = new();
		x_Toa_Do_Class498.Rxjh_Map = 40101;
		x_Toa_Do_Class498.Rxjh_X = -290f;
		x_Toa_Do_Class498.Rxjh_Y = 588f;
		x_Toa_Do_Class498.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class498);
		X_Toa_Do_Class x_Toa_Do_Class499 = new();
		x_Toa_Do_Class499.Rxjh_Map = 40101;
		x_Toa_Do_Class499.Rxjh_X = -280f;
		x_Toa_Do_Class499.Rxjh_Y = 585f;
		x_Toa_Do_Class499.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class499);
		X_Toa_Do_Class x_Toa_Do_Class500 = new();
		x_Toa_Do_Class500.Rxjh_Map = 40101;
		x_Toa_Do_Class500.Rxjh_X = -279f;
		x_Toa_Do_Class500.Rxjh_Y = 599f;
		x_Toa_Do_Class500.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class500);
		X_Toa_Do_Class x_Toa_Do_Class501 = new();
		x_Toa_Do_Class501.Rxjh_Map = 40101;
		x_Toa_Do_Class501.Rxjh_X = -278f;
		x_Toa_Do_Class501.Rxjh_Y = 616f;
		x_Toa_Do_Class501.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class501);
		X_Toa_Do_Class x_Toa_Do_Class502 = new();
		x_Toa_Do_Class502.Rxjh_Map = 40101;
		x_Toa_Do_Class502.Rxjh_X = -277f;
		x_Toa_Do_Class502.Rxjh_Y = 635f;
		x_Toa_Do_Class502.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class502);
		X_Toa_Do_Class x_Toa_Do_Class503 = new();
		x_Toa_Do_Class503.Rxjh_Map = 40101;
		x_Toa_Do_Class503.Rxjh_X = -283f;
		x_Toa_Do_Class503.Rxjh_Y = 646f;
		x_Toa_Do_Class503.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class503);
		X_Toa_Do_Class x_Toa_Do_Class504 = new();
		x_Toa_Do_Class504.Rxjh_Map = 40101;
		x_Toa_Do_Class504.Rxjh_X = -280f;
		x_Toa_Do_Class504.Rxjh_Y = 663f;
		x_Toa_Do_Class504.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class504);
		X_Toa_Do_Class x_Toa_Do_Class505 = new();
		x_Toa_Do_Class505.Rxjh_Map = 40101;
		x_Toa_Do_Class505.Rxjh_X = -296f;
		x_Toa_Do_Class505.Rxjh_Y = 362f;
		x_Toa_Do_Class505.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class505);
		X_Toa_Do_Class x_Toa_Do_Class506 = new();
		x_Toa_Do_Class506.Rxjh_Map = 40101;
		x_Toa_Do_Class506.Rxjh_X = -296f;
		x_Toa_Do_Class506.Rxjh_Y = 346f;
		x_Toa_Do_Class506.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class506);
		X_Toa_Do_Class x_Toa_Do_Class507 = new();
		x_Toa_Do_Class507.Rxjh_Map = 40101;
		x_Toa_Do_Class507.Rxjh_X = -296f;
		x_Toa_Do_Class507.Rxjh_Y = 332f;
		x_Toa_Do_Class507.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class507);
		X_Toa_Do_Class x_Toa_Do_Class508 = new();
		x_Toa_Do_Class508.Rxjh_Map = 40101;
		x_Toa_Do_Class508.Rxjh_X = -296f;
		x_Toa_Do_Class508.Rxjh_Y = 313f;
		x_Toa_Do_Class508.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class508);
		X_Toa_Do_Class x_Toa_Do_Class509 = new();
		x_Toa_Do_Class509.Rxjh_Map = 40101;
		x_Toa_Do_Class509.Rxjh_X = -296f;
		x_Toa_Do_Class509.Rxjh_Y = 301f;
		x_Toa_Do_Class509.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class509);
		X_Toa_Do_Class x_Toa_Do_Class510 = new();
		x_Toa_Do_Class510.Rxjh_Map = 40101;
		x_Toa_Do_Class510.Rxjh_X = -293f;
		x_Toa_Do_Class510.Rxjh_Y = 287f;
		x_Toa_Do_Class510.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class510);
		X_Toa_Do_Class x_Toa_Do_Class511 = new();
		x_Toa_Do_Class511.Rxjh_Map = 40101;
		x_Toa_Do_Class511.Rxjh_X = -304f;
		x_Toa_Do_Class511.Rxjh_Y = 288f;
		x_Toa_Do_Class511.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class511);
		X_Toa_Do_Class x_Toa_Do_Class512 = new();
		x_Toa_Do_Class512.Rxjh_Map = 40101;
		x_Toa_Do_Class512.Rxjh_X = -306f;
		x_Toa_Do_Class512.Rxjh_Y = 319f;
		x_Toa_Do_Class512.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class512);
		X_Toa_Do_Class x_Toa_Do_Class513 = new();
		x_Toa_Do_Class513.Rxjh_Map = 40101;
		x_Toa_Do_Class513.Rxjh_X = -310f;
		x_Toa_Do_Class513.Rxjh_Y = 337f;
		x_Toa_Do_Class513.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class513);
		X_Toa_Do_Class x_Toa_Do_Class514 = new();
		x_Toa_Do_Class514.Rxjh_Map = 40101;
		x_Toa_Do_Class514.Rxjh_X = -309f;
		x_Toa_Do_Class514.Rxjh_Y = 349f;
		x_Toa_Do_Class514.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class514);
		X_Toa_Do_Class x_Toa_Do_Class515 = new();
		x_Toa_Do_Class515.Rxjh_Map = 40101;
		x_Toa_Do_Class515.Rxjh_X = -306f;
		x_Toa_Do_Class515.Rxjh_Y = 362f;
		x_Toa_Do_Class515.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class515);
		X_Toa_Do_Class x_Toa_Do_Class516 = new();
		x_Toa_Do_Class516.Rxjh_Map = 40101;
		x_Toa_Do_Class516.Rxjh_X = -91f;
		x_Toa_Do_Class516.Rxjh_Y = 791f;
		x_Toa_Do_Class516.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class516);
		X_Toa_Do_Class x_Toa_Do_Class517 = new();
		x_Toa_Do_Class517.Rxjh_Map = 40101;
		x_Toa_Do_Class517.Rxjh_X = -101f;
		x_Toa_Do_Class517.Rxjh_Y = 791f;
		x_Toa_Do_Class517.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class517);
		X_Toa_Do_Class x_Toa_Do_Class518 = new();
		x_Toa_Do_Class518.Rxjh_Map = 40101;
		x_Toa_Do_Class518.Rxjh_X = -111f;
		x_Toa_Do_Class518.Rxjh_Y = 791f;
		x_Toa_Do_Class518.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class518);
		X_Toa_Do_Class x_Toa_Do_Class519 = new();
		x_Toa_Do_Class519.Rxjh_Map = 40101;
		x_Toa_Do_Class519.Rxjh_X = -121f;
		x_Toa_Do_Class519.Rxjh_Y = 791f;
		x_Toa_Do_Class519.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class519);
		X_Toa_Do_Class x_Toa_Do_Class520 = new();
		x_Toa_Do_Class520.Rxjh_Map = 40101;
		x_Toa_Do_Class520.Rxjh_X = -131f;
		x_Toa_Do_Class520.Rxjh_Y = 791f;
		x_Toa_Do_Class520.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class520);
		X_Toa_Do_Class x_Toa_Do_Class521 = new();
		x_Toa_Do_Class521.Rxjh_Map = 40101;
		x_Toa_Do_Class521.Rxjh_X = -141f;
		x_Toa_Do_Class521.Rxjh_Y = 791f;
		x_Toa_Do_Class521.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class521);
		X_Toa_Do_Class x_Toa_Do_Class522 = new();
		x_Toa_Do_Class522.Rxjh_Map = 40101;
		x_Toa_Do_Class522.Rxjh_X = -151f;
		x_Toa_Do_Class522.Rxjh_Y = 791f;
		x_Toa_Do_Class522.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class522);
		X_Toa_Do_Class x_Toa_Do_Class523 = new();
		x_Toa_Do_Class523.Rxjh_Map = 40101;
		x_Toa_Do_Class523.Rxjh_X = -161f;
		x_Toa_Do_Class523.Rxjh_Y = 791f;
		x_Toa_Do_Class523.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class523);
		X_Toa_Do_Class x_Toa_Do_Class524 = new();
		x_Toa_Do_Class524.Rxjh_Map = 40101;
		x_Toa_Do_Class524.Rxjh_X = -171f;
		x_Toa_Do_Class524.Rxjh_Y = 791f;
		x_Toa_Do_Class524.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class524);
		X_Toa_Do_Class x_Toa_Do_Class525 = new();
		x_Toa_Do_Class525.Rxjh_Map = 40101;
		x_Toa_Do_Class525.Rxjh_X = -93f;
		x_Toa_Do_Class525.Rxjh_Y = 780f;
		x_Toa_Do_Class525.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class525);
		X_Toa_Do_Class x_Toa_Do_Class526 = new();
		x_Toa_Do_Class526.Rxjh_Map = 40101;
		x_Toa_Do_Class526.Rxjh_X = -103f;
		x_Toa_Do_Class526.Rxjh_Y = 780f;
		x_Toa_Do_Class526.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class526);
		X_Toa_Do_Class x_Toa_Do_Class527 = new();
		x_Toa_Do_Class527.Rxjh_Map = 40101;
		x_Toa_Do_Class527.Rxjh_X = -113f;
		x_Toa_Do_Class527.Rxjh_Y = 780f;
		x_Toa_Do_Class527.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class527);
		X_Toa_Do_Class x_Toa_Do_Class528 = new();
		x_Toa_Do_Class528.Rxjh_Map = 40101;
		x_Toa_Do_Class528.Rxjh_X = -123f;
		x_Toa_Do_Class528.Rxjh_Y = 780f;
		x_Toa_Do_Class528.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class528);
		X_Toa_Do_Class x_Toa_Do_Class529 = new();
		x_Toa_Do_Class529.Rxjh_Map = 40101;
		x_Toa_Do_Class529.Rxjh_X = -133f;
		x_Toa_Do_Class529.Rxjh_Y = 780f;
		x_Toa_Do_Class529.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class529);
		X_Toa_Do_Class x_Toa_Do_Class530 = new();
		x_Toa_Do_Class530.Rxjh_Map = 40101;
		x_Toa_Do_Class530.Rxjh_X = -143f;
		x_Toa_Do_Class530.Rxjh_Y = 780f;
		x_Toa_Do_Class530.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class530);
		X_Toa_Do_Class x_Toa_Do_Class531 = new();
		x_Toa_Do_Class531.Rxjh_Map = 40101;
		x_Toa_Do_Class531.Rxjh_X = -153f;
		x_Toa_Do_Class531.Rxjh_Y = 780f;
		x_Toa_Do_Class531.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class531);
		X_Toa_Do_Class x_Toa_Do_Class532 = new();
		x_Toa_Do_Class532.Rxjh_Map = 40101;
		x_Toa_Do_Class532.Rxjh_X = -163f;
		x_Toa_Do_Class532.Rxjh_Y = 780f;
		x_Toa_Do_Class532.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class532);
		X_Toa_Do_Class x_Toa_Do_Class533 = new();
		x_Toa_Do_Class533.Rxjh_Map = 40101;
		x_Toa_Do_Class533.Rxjh_X = -173f;
		x_Toa_Do_Class533.Rxjh_Y = 780f;
		x_Toa_Do_Class533.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class533);
		X_Toa_Do_Class x_Toa_Do_Class534 = new();
		x_Toa_Do_Class534.Rxjh_Map = 40101;
		x_Toa_Do_Class534.Rxjh_X = 312f;
		x_Toa_Do_Class534.Rxjh_Y = 599f;
		x_Toa_Do_Class534.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class534);
		X_Toa_Do_Class x_Toa_Do_Class535 = new();
		x_Toa_Do_Class535.Rxjh_Map = 40101;
		x_Toa_Do_Class535.Rxjh_X = 312f;
		x_Toa_Do_Class535.Rxjh_Y = 610f;
		x_Toa_Do_Class535.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class535);
		X_Toa_Do_Class x_Toa_Do_Class536 = new();
		x_Toa_Do_Class536.Rxjh_Map = 40101;
		x_Toa_Do_Class536.Rxjh_X = 312f;
		x_Toa_Do_Class536.Rxjh_Y = 620f;
		x_Toa_Do_Class536.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class536);
		X_Toa_Do_Class x_Toa_Do_Class537 = new();
		x_Toa_Do_Class537.Rxjh_Map = 40101;
		x_Toa_Do_Class537.Rxjh_X = 312f;
		x_Toa_Do_Class537.Rxjh_Y = 630f;
		x_Toa_Do_Class537.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class537);
		X_Toa_Do_Class x_Toa_Do_Class538 = new();
		x_Toa_Do_Class538.Rxjh_Map = 40101;
		x_Toa_Do_Class538.Rxjh_X = 312f;
		x_Toa_Do_Class538.Rxjh_Y = 639f;
		x_Toa_Do_Class538.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class538);
		X_Toa_Do_Class x_Toa_Do_Class539 = new();
		x_Toa_Do_Class539.Rxjh_Map = 40101;
		x_Toa_Do_Class539.Rxjh_X = 312f;
		x_Toa_Do_Class539.Rxjh_Y = 639f;
		x_Toa_Do_Class539.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class539);
		X_Toa_Do_Class x_Toa_Do_Class540 = new();
		x_Toa_Do_Class540.Rxjh_Map = 40101;
		x_Toa_Do_Class540.Rxjh_X = 327f;
		x_Toa_Do_Class540.Rxjh_Y = 653f;
		x_Toa_Do_Class540.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class540);
		X_Toa_Do_Class x_Toa_Do_Class541 = new();
		x_Toa_Do_Class541.Rxjh_Map = 40101;
		x_Toa_Do_Class541.Rxjh_X = 327f;
		x_Toa_Do_Class541.Rxjh_Y = 662f;
		x_Toa_Do_Class541.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class541);
		X_Toa_Do_Class x_Toa_Do_Class542 = new();
		x_Toa_Do_Class542.Rxjh_Map = 40101;
		x_Toa_Do_Class542.Rxjh_X = 311f;
		x_Toa_Do_Class542.Rxjh_Y = 289f;
		x_Toa_Do_Class542.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class542);
		X_Toa_Do_Class x_Toa_Do_Class543 = new();
		x_Toa_Do_Class543.Rxjh_Map = 40101;
		x_Toa_Do_Class543.Rxjh_X = 314f;
		x_Toa_Do_Class543.Rxjh_Y = 304f;
		x_Toa_Do_Class543.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class543);
		X_Toa_Do_Class x_Toa_Do_Class544 = new();
		x_Toa_Do_Class544.Rxjh_Map = 40101;
		x_Toa_Do_Class544.Rxjh_X = 315f;
		x_Toa_Do_Class544.Rxjh_Y = 322f;
		x_Toa_Do_Class544.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class544);
		X_Toa_Do_Class x_Toa_Do_Class545 = new();
		x_Toa_Do_Class545.Rxjh_Map = 40101;
		x_Toa_Do_Class545.Rxjh_X = 315f;
		x_Toa_Do_Class545.Rxjh_Y = 340f;
		x_Toa_Do_Class545.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class545);
		X_Toa_Do_Class x_Toa_Do_Class546 = new();
		x_Toa_Do_Class546.Rxjh_Map = 40101;
		x_Toa_Do_Class546.Rxjh_X = 315f;
		x_Toa_Do_Class546.Rxjh_Y = 359f;
		x_Toa_Do_Class546.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class546);
		X_Toa_Do_Class x_Toa_Do_Class547 = new();
		x_Toa_Do_Class547.Rxjh_Map = 40101;
		x_Toa_Do_Class547.Rxjh_X = 309f;
		x_Toa_Do_Class547.Rxjh_Y = 372f;
		x_Toa_Do_Class547.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class547);
		X_Toa_Do_Class x_Toa_Do_Class548 = new();
		x_Toa_Do_Class548.Rxjh_Map = 40101;
		x_Toa_Do_Class548.Rxjh_X = 301f;
		x_Toa_Do_Class548.Rxjh_Y = 364f;
		x_Toa_Do_Class548.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class548);
		X_Toa_Do_Class x_Toa_Do_Class549 = new();
		x_Toa_Do_Class549.Rxjh_Map = 40101;
		x_Toa_Do_Class549.Rxjh_X = 303f;
		x_Toa_Do_Class549.Rxjh_Y = 346f;
		x_Toa_Do_Class549.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class549);
		X_Toa_Do_Class x_Toa_Do_Class550 = new();
		x_Toa_Do_Class550.Rxjh_Map = 40101;
		x_Toa_Do_Class550.Rxjh_X = 304f;
		x_Toa_Do_Class550.Rxjh_Y = 331f;
		x_Toa_Do_Class550.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class550);
		X_Toa_Do_Class x_Toa_Do_Class551 = new();
		x_Toa_Do_Class551.Rxjh_Map = 40101;
		x_Toa_Do_Class551.Rxjh_X = 305f;
		x_Toa_Do_Class551.Rxjh_Y = 314f;
		x_Toa_Do_Class551.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class551);
		X_Toa_Do_Class x_Toa_Do_Class552 = new();
		x_Toa_Do_Class552.Rxjh_Map = 40101;
		x_Toa_Do_Class552.Rxjh_X = 306f;
		x_Toa_Do_Class552.Rxjh_Y = 297f;
		x_Toa_Do_Class552.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class552);
		X_Toa_Do_Class x_Toa_Do_Class553 = new();
		x_Toa_Do_Class553.Rxjh_Map = 40101;
		x_Toa_Do_Class553.Rxjh_X = 303f;
		x_Toa_Do_Class553.Rxjh_Y = 289f;
		x_Toa_Do_Class553.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class553);
		X_Toa_Do_Class x_Toa_Do_Class554 = new();
		x_Toa_Do_Class554.Rxjh_Map = 40101;
		x_Toa_Do_Class554.Rxjh_X = 187f;
		x_Toa_Do_Class554.Rxjh_Y = 774f;
		x_Toa_Do_Class554.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class554);
		X_Toa_Do_Class x_Toa_Do_Class555 = new();
		x_Toa_Do_Class555.Rxjh_Map = 40101;
		x_Toa_Do_Class555.Rxjh_X = 171f;
		x_Toa_Do_Class555.Rxjh_Y = 776f;
		x_Toa_Do_Class555.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class555);
		X_Toa_Do_Class x_Toa_Do_Class556 = new();
		x_Toa_Do_Class556.Rxjh_Map = 40101;
		x_Toa_Do_Class556.Rxjh_X = 153f;
		x_Toa_Do_Class556.Rxjh_Y = 778f;
		x_Toa_Do_Class556.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class556);
		X_Toa_Do_Class x_Toa_Do_Class557 = new();
		x_Toa_Do_Class557.Rxjh_Map = 40101;
		x_Toa_Do_Class557.Rxjh_X = 135f;
		x_Toa_Do_Class557.Rxjh_Y = 775f;
		x_Toa_Do_Class557.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class557);
		X_Toa_Do_Class x_Toa_Do_Class558 = new();
		x_Toa_Do_Class558.Rxjh_Map = 40101;
		x_Toa_Do_Class558.Rxjh_X = 118f;
		x_Toa_Do_Class558.Rxjh_Y = 776f;
		x_Toa_Do_Class558.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class558);
		X_Toa_Do_Class x_Toa_Do_Class559 = new();
		x_Toa_Do_Class559.Rxjh_Map = 40101;
		x_Toa_Do_Class559.Rxjh_X = 120f;
		x_Toa_Do_Class559.Rxjh_Y = 788f;
		x_Toa_Do_Class559.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class559);
		X_Toa_Do_Class x_Toa_Do_Class560 = new();
		x_Toa_Do_Class560.Rxjh_Map = 40101;
		x_Toa_Do_Class560.Rxjh_X = 137f;
		x_Toa_Do_Class560.Rxjh_Y = 788f;
		x_Toa_Do_Class560.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class560);
		X_Toa_Do_Class x_Toa_Do_Class561 = new();
		x_Toa_Do_Class561.Rxjh_Map = 40101;
		x_Toa_Do_Class561.Rxjh_X = 153f;
		x_Toa_Do_Class561.Rxjh_Y = 787f;
		x_Toa_Do_Class561.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class561);
		X_Toa_Do_Class x_Toa_Do_Class562 = new();
		x_Toa_Do_Class562.Rxjh_Map = 40101;
		x_Toa_Do_Class562.Rxjh_X = 171f;
		x_Toa_Do_Class562.Rxjh_Y = 787f;
		x_Toa_Do_Class562.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class562);
		X_Toa_Do_Class x_Toa_Do_Class563 = new();
		x_Toa_Do_Class563.Rxjh_Map = 40101;
		x_Toa_Do_Class563.Rxjh_X = 187f;
		x_Toa_Do_Class563.Rxjh_Y = 787f;
		x_Toa_Do_Class563.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class563);
		X_Toa_Do_Class x_Toa_Do_Class564 = new();
		x_Toa_Do_Class564.Rxjh_Map = 40101;
		x_Toa_Do_Class564.Rxjh_X = 25f;
		x_Toa_Do_Class564.Rxjh_Y = 476f;
		x_Toa_Do_Class564.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class564);
		X_Toa_Do_Class x_Toa_Do_Class565 = new();
		x_Toa_Do_Class565.Rxjh_Map = 40101;
		x_Toa_Do_Class565.Rxjh_X = 23f;
		x_Toa_Do_Class565.Rxjh_Y = 489f;
		x_Toa_Do_Class565.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class565);
		X_Toa_Do_Class x_Toa_Do_Class566 = new();
		x_Toa_Do_Class566.Rxjh_Map = 40101;
		x_Toa_Do_Class566.Rxjh_X = 17f;
		x_Toa_Do_Class566.Rxjh_Y = 496f;
		x_Toa_Do_Class566.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class566);
		X_Toa_Do_Class x_Toa_Do_Class567 = new();
		x_Toa_Do_Class567.Rxjh_Map = 40101;
		x_Toa_Do_Class567.Rxjh_X = 8f;
		x_Toa_Do_Class567.Rxjh_Y = 493f;
		x_Toa_Do_Class567.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class567);
		X_Toa_Do_Class x_Toa_Do_Class568 = new();
		x_Toa_Do_Class568.Rxjh_Map = 40101;
		x_Toa_Do_Class568.Rxjh_X = -1f;
		x_Toa_Do_Class568.Rxjh_Y = 484f;
		x_Toa_Do_Class568.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class568);
		X_Toa_Do_Class x_Toa_Do_Class569 = new();
		x_Toa_Do_Class569.Rxjh_Map = 40101;
		x_Toa_Do_Class569.Rxjh_X = -3f;
		x_Toa_Do_Class569.Rxjh_Y = 482f;
		x_Toa_Do_Class569.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class569);
		X_Toa_Do_Class x_Toa_Do_Class570 = new();
		x_Toa_Do_Class570.Rxjh_Map = 40101;
		x_Toa_Do_Class570.Rxjh_X = -4f;
		x_Toa_Do_Class570.Rxjh_Y = 500f;
		x_Toa_Do_Class570.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class570);
		X_Toa_Do_Class x_Toa_Do_Class571 = new();
		x_Toa_Do_Class571.Rxjh_Map = 40101;
		x_Toa_Do_Class571.Rxjh_X = 326f;
		x_Toa_Do_Class571.Rxjh_Y = 620f;
		x_Toa_Do_Class571.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(x_Toa_Do_Class571);
		X_Toa_Do_Class item = new();
		x_Toa_Do_Class571.Rxjh_Map = 40101;
		x_Toa_Do_Class571.Rxjh_X = -27f;
		x_Toa_Do_Class571.Rxjh_Y = -481f;
		x_Toa_Do_Class571.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(item);
		X_Toa_Do_Class item2 = new();
		x_Toa_Do_Class571.Rxjh_Map = 40101;
		x_Toa_Do_Class571.Rxjh_X = -25f;
		x_Toa_Do_Class571.Rxjh_Y = -497f;
		x_Toa_Do_Class571.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(item2);
		X_Toa_Do_Class item3 = new();
		x_Toa_Do_Class571.Rxjh_Map = 40101;
		x_Toa_Do_Class571.Rxjh_X = -9f;
		x_Toa_Do_Class571.Rxjh_Y = 490f;
		x_Toa_Do_Class571.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(item3);
		X_Toa_Do_Class item4 = new();
		x_Toa_Do_Class571.Rxjh_Map = 40101;
		x_Toa_Do_Class571.Rxjh_X = -7f;
		x_Toa_Do_Class571.Rxjh_Y = 472f;
		x_Toa_Do_Class571.Rxjh_Z = 15f;
		World.DCH_ToaDo_TanHinh.Add(item4);
	}
}
