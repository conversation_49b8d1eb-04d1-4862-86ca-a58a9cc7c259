using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using HeroYulgang.Helpers;
using RxjhServer.ManageZone;

namespace RxjhServer
{
    /// <summary>
    /// Phần mở rộng của lớp Players để xử lý Zone liên server
    /// </summary>
    public partial class Players
    {

        public void GetReviewScopeNpcCrossServer()
        {
            try
            {
                if (this.CurrentZone == null || !this.CurrentZone.IsCrossServer)
                {
                    // Nếu không phải zone liên server, sử dụng phương thức gốc
                     this.GetReviewScopeNpc(true);
                    return;
                }
                // Gửi yêu cầu lấy nhân vật từ zone khác
                World.conn.SendCrossServerAction(
                    this.CurrentZone.ID,
                    "GET_NPCS",
                    this.SessionID,
                    this.NhanVatToaDo_X,
                    this.NhanVatToaDo_Y,
                    this.NhanVatToaDo_Z,
                    this.NhanVatToaDo_BanDo
                );
                // TODO: có thể cần lấy củ<PERSON> cả server hiện tại
            }
            catch (System.Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "GetTheReviewRangePlayersCrossServer error " +ex.Message);
            }
        }

        public void GetTheReviewRangePlayersCrossServer(){
            try
            {
                if (this.CurrentZone == null || !this.CurrentZone.IsCrossServer)
                {
                    // Nếu không phải zone liên server, sử dụng phương thức gốc
                     this.GetTheReviewRangePlayers(true);
                    return;
                }
                // Gửi yêu cầu lấy nhân vật từ zone khác
                World.conn.SendCrossServerAction(
                    this.CurrentZone.ID,
                    "GET_PLAYERS",
                    this.SessionID,
                    this.NhanVatToaDo_X,
                    this.NhanVatToaDo_Y,
                    this.NhanVatToaDo_Z,
                    this.NhanVatToaDo_BanDo
                );
                // TODO: có thể cần lấy của cả server hiện tại
            }
            catch (System.Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "GetTheReviewRangePlayersCrossServer error " +ex.Message);
            }
        }

        public void RemovePlayerCrossServer()
        {
            try
            {
                if (this.CurrentZone == null || !this.CurrentZone.IsCrossServer)
                {
                    return;
                }
                // Gửi yêu cầu xóa người chơi từ zone liên server
                World.conn.SendCrossServerAction(
                    this.CurrentZone.ID,
                    "REMOVE_PLAYER",
                    this.SessionID
                );
            }
            catch (System.Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "RemovePlayerCrossServer error " +ex.Message);
            }
        }

        /// <summary>
        /// Kiểm tra xem người chơi có thể tấn công người chơi khác trong Zone liên server không
        /// </summary>
        /// <param name="target">Người chơi bị tấn công</param>
        /// <returns>true nếu có thể tấn công, false nếu không thể</returns>
        public bool CanAttackPlayerCrossServer(Players target)
        {
            try
            {
                if (target == null)
                    return false;

                // Nếu một trong hai người chơi không có zone, mặc định cho phép tấn công
                if (this.CurrentZone == null || target.CurrentZone == null)
                    return true;

                // Nếu không phải zone liên server, sử dụng quy tắc thông thường
                if (!this.CurrentZone.IsCrossServer && !target.CurrentZone.IsCrossServer)
                    return this.CanAttackPlayer(target);

                // Nếu khác zone, không thể tấn công
                if (this.CurrentZone.ID != target.CurrentZone.ID)
                {
                    this.HeThongNhacNho("Bạn không thể tấn công người chơi ở zone khác");
                    return false;
                }

                // Nếu cùng zone liên server, kiểm tra điều kiện truy cập
                if (this.CurrentZone.IsCrossServer)
                {
                    // Kiểm tra điều kiện truy cập
                    if (!ZoneManager.Instance.CanAccessZone(this, this.CurrentZone) ||
                        !ZoneManager.Instance.CanAccessZone(target, target.CurrentZone))
                    {
                        this.HeThongNhacNho("Một trong hai người chơi không đủ điều kiện để ở trong zone này");
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"CanAttackPlayerCrossServer error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Kiểm tra xem người chơi có thể tấn công NPC trong Zone liên server không
        /// </summary>
        /// <param name="npc">NPC bị tấn công</param>
        /// <returns>true nếu có thể tấn công, false nếu không thể</returns>
        public bool CanAttackNpcCrossServer(NpcClass npc)
        {
            try
            {
                if (npc == null)
                    return false;

                // Nếu người chơi không có zone, mặc định cho phép tấn công
                if (this.CurrentZone == null)
                    return true;

                // Nếu NPC không có zone, mặc định cho phép tấn công
                if (npc.CurrentZone == null)
                    return true;

                // Nếu không phải zone liên server, sử dụng quy tắc thông thường
                if (!this.CurrentZone.IsCrossServer && !npc.CurrentZone.IsCrossServer)
                    return this.CanAttackNpc(npc);

                // Nếu khác zone, không thể tấn công
                if (this.CurrentZone.ID != npc.CurrentZone.ID)
                {
                    this.HeThongNhacNho("Bạn không thể tấn công NPC ở zone khác");
                    return false;
                }

                // Nếu cùng zone liên server, kiểm tra điều kiện truy cập
                if (this.CurrentZone.IsCrossServer)
                {
                    // Kiểm tra điều kiện truy cập
                    if (!ZoneManager.Instance.CanAccessZone(this, this.CurrentZone))
                    {
                        this.HeThongNhacNho("Bạn không đủ điều kiện để ở trong zone này");
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"CanAttackNpcCrossServer error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Xử lý tấn công người chơi trong Zone liên server
        /// </summary>
        /// <param name="target">Người chơi bị tấn công</param>
        /// <param name="damage">Sát thương</param>
        /// <returns>true nếu tấn công thành công, false nếu không thành công</returns>
        public bool AttackPlayerCrossServer(Players target, int damage)
        {
            try
            {
                if (target == null)
                    return false;

                // Kiểm tra xem có thể tấn công không
                if (!CanAttackPlayerCrossServer(target))
                    return false;

                // Nếu cùng server, xử lý tấn công bình thường
                if (this.CurrentZone.OriginServerID == World.ServerID.ToString())
                {
                    // Xử lý tấn công bình thường
                    // Đây chỉ là giả lập, cần triển khai chi tiết hơn
                    return true;
                }

                // Nếu khác server, gửi thông tin tấn công đến LoginServer
                World.conn.SendCrossServerAction(
                    this.CurrentZone.ID,
                    "ATTACK",
                    this.SessionID,
                    0, // 0: Player
                    target.SessionID,
                    damage
                );

                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"AttackPlayerCrossServer error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Xử lý tấn công NPC trong Zone liên server
        /// </summary>
        /// <param name="npc">NPC bị tấn công</param>
        /// <param name="damage">Sát thương</param>
        /// <returns>true nếu tấn công thành công, false nếu không thành công</returns>
        public bool AttackNpcCrossServer(NpcClass npc, int damage)
        {
            try
            {
                if (npc == null)
                    return false;

                // Kiểm tra xem có thể tấn công không
                if (!CanAttackNpcCrossServer(npc))
                    return false;

                // Nếu cùng server, xử lý tấn công bình thường
                if (this.CurrentZone.OriginServerID == World.ServerID.ToString())
                {
                    // Xử lý tấn công bình thường
                    // Đây chỉ là giả lập, cần triển khai chi tiết hơn
                    return true;
                }

                // Nếu khác server, gửi thông tin tấn công đến LoginServer
                World.conn.SendCrossServerAction(
                    this.CurrentZone.ID,
                    "ATTACK",
                    this.SessionID,
                    1, // 1: NPC
                    npc.NPC_SessionID,
                    damage
                );

                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"AttackNpcCrossServer error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Xử lý di chuyển trong Zone liên server
        /// </summary>
        /// <param name="x">Tọa độ X</param>
        /// <param name="y">Tọa độ Y</param>
        /// <param name="z">Tọa độ Z</param>
        /// <returns>true nếu di chuyển thành công, false nếu không thành công</returns>
        public bool MoveCrossServer(float x, float y, float z)
        {
            try
            {
                // Nếu không có zone hoặc không phải zone liên server, xử lý di chuyển bình thường
                if (this.CurrentZone == null || !this.CurrentZone.IsCrossServer)
                    return true;

                // Nếu cùng server, xử lý di chuyển bình thường
                if (this.CurrentZone.OriginServerID == World.ServerID.ToString())
                    return true;

                // Nếu khác server, gửi thông tin di chuyển đến LoginServer
                World.conn.SendCrossServerAction(
                    this.CurrentZone.ID,
                    "MOVE",
                    this.SessionID,
                    x,
                    y,
                    z,
                    this.NhanVatToaDo_BanDo
                );

                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"MoveCrossServer error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Hook vào phương thức di chuyển để xử lý di chuyển trong Zone liên server
        /// </summary>
        /// <param name="x">Tọa độ X</param>
        /// <param name="y">Tọa độ Y</param>
        /// <param name="z">Tọa độ Z</param>
        public void HookMove(float x, float y, float z)
        {
            try
            {
                // Xử lý di chuyển trong Zone liên server
                MoveCrossServer(x, y, z);

                // Kiểm tra và cập nhật zone của người chơi dựa theo vị trí
                ZoneManager.Instance.CheckPlayerZonePosition(this);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"HookMove error: {ex.Message}");
            }
        }

        /// <summary>
        /// Hook vào phương thức tấn công để xử lý tấn công trong Zone liên server
        /// </summary>
        /// <param name="target">Đối tượng bị tấn công</param>
        /// <param name="damage">Sát thương</param>
        public void HookAttack(object target, int damage)
        {
            try
            {
                // Kiểm tra xem người chơi có đang ở trong zone liên server không
                if (this.CurrentZone == null || !this.CurrentZone.IsCrossServer)
                    return;

                // Xử lý tấn công trong Zone liên server
                if (target is Players playerTarget)
                {
                    // Kiểm tra xem có thể tấn công không
                    if (!CanAttackPlayerCrossServer(playerTarget))
                    {
                        this.HeThongNhacNho("Bạn không thể tấn công người chơi này trong zone liên server");
                        return;
                    }

                    // Xử lý tấn công
                    AttackPlayerCrossServer(playerTarget, damage);

                    // Ghi log
                    LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Người chơi {this.CharacterName} tấn công người chơi {playerTarget.CharacterName} trong zone liên server {this.CurrentZone.ID}");
                }
                else if (target is NpcClass npcTarget)
                {
                    // Kiểm tra xem có thể tấn công không
                    if (!CanAttackNpcCrossServer(npcTarget))
                    {
                        this.HeThongNhacNho("Bạn không thể tấn công NPC này trong zone liên server");
                        return;
                    }

                    // Xử lý tấn công
                    AttackNpcCrossServer(npcTarget, damage);

                    // Ghi log
                    LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Người chơi {this.CharacterName} tấn công NPC {npcTarget.Name} trong zone liên server {this.CurrentZone.ID}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"HookAttack error: {ex.Message}");
            }
        }
    }
}
